{"version": 3, "sources": ["../../../../src/core/utils/logging/getStatusCodeColor.ts"], "sourcesContent": ["export enum StatusCodeColor {\n  Success = '#69AB32',\n  Warning = '#F0BB4B',\n  Danger = '#E95F5D',\n}\n\n/**\n * Returns a HEX color for a given response status code number.\n */\nexport function getStatusCodeColor(status: number): StatusCodeColor {\n  if (status < 300) {\n    return StatusCodeColor.Success\n  }\n\n  if (status < 400) {\n    return StatusCodeColor.Warning\n  }\n\n  return StatusCodeColor.Danger\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAK,kBAAL,kBAAKA,qBAAL;AACL,EAAAA,iBAAA,aAAU;AACV,EAAAA,iBAAA,aAAU;AACV,EAAAA,iBAAA,YAAS;AAHC,SAAAA;AAAA,GAAA;AASL,SAAS,mBAAmB,QAAiC;AAClE,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;", "names": ["StatusCodeColor"]}