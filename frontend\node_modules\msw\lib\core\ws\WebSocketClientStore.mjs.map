{"version": 3, "sources": ["../../../src/core/ws/WebSocketClientStore.ts"], "sourcesContent": ["import type { WebSocketClientConnectionProtocol } from '@mswjs/interceptors/WebSocket'\n\nexport interface SerializedWebSocketClient {\n  id: string\n  url: string\n}\n\nexport abstract class WebSocketClientStore {\n  public abstract add(client: WebSocketClientConnectionProtocol): Promise<void>\n\n  public abstract getAll(): Promise<Array<SerializedWebSocketClient>>\n\n  public abstract deleteMany(clientIds: Array<string>): Promise<void>\n}\n"], "mappings": "AAOO,MAAe,qBAAqB;AAM3C;", "names": []}