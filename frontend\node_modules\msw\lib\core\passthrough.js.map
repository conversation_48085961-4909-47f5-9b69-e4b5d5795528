{"version": 3, "sources": ["../../src/core/passthrough.ts"], "sourcesContent": ["import type { HttpResponse } from './HttpResponse'\n\n/**\n * Performs the intercepted request as-is.\n *\n * This stops request handler lookup so no other handlers\n * can affect this request past this point.\n * Unlike `bypass()`, this will not trigger an additional request.\n *\n * @example\n * http.get('/resource', () => {\n *   return passthrough()\n * })\n *\n * @see {@link https://mswjs.io/docs/api/passthrough `passthrough()` API reference}\n */\nexport function passthrough(): HttpResponse<any> {\n  return new Response(null, {\n    status: 302,\n    statusText: 'Passthrough',\n    headers: {\n      'x-msw-intention': 'passthrough',\n    },\n  }) as HttpResponse<any>\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBO,SAAS,cAAiC;AAC/C,SAAO,IAAI,SAAS,MAAM;AAAA,IACxB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,SAAS;AAAA,MACP,mBAAmB;AAAA,IACrB;AAAA,EACF,CAAC;AACH;", "names": []}