{"version": 3, "sources": ["../../../../src/core/utils/internal/parseMultipartData.ts"], "sourcesContent": ["import { stringToHeaders } from 'headers-polyfill'\nimport { DefaultRequestMultipartBody } from '../../handlers/RequestHandler'\n\ninterface ParsedContentHeaders {\n  name: string\n  filename?: string\n  contentType: string\n}\n\ninterface ContentDispositionDirective {\n  [key: string]: string | undefined\n  name: string\n  filename?: string\n  'form-data': string\n}\n\nfunction parseContentHeaders(headersString: string): ParsedContentHeaders {\n  const headers = stringToHeaders(headersString)\n  const contentType = headers.get('content-type') || 'text/plain'\n  const disposition = headers.get('content-disposition')\n\n  if (!disposition) {\n    throw new Error('\"Content-Disposition\" header is required.')\n  }\n\n  const directives = disposition.split(';').reduce((acc, chunk) => {\n    const [name, ...rest] = chunk.trim().split('=')\n    acc[name] = rest.join('=')\n    return acc\n  }, {} as ContentDispositionDirective)\n\n  const name = directives.name?.slice(1, -1)\n  const filename = directives.filename?.slice(1, -1)\n\n  return {\n    name,\n    filename,\n    contentType,\n  }\n}\n\n/**\n * Parses a given string as a multipart/form-data.\n * Does not throw an exception on an invalid multipart string.\n */\nexport function parseMultipartData<T extends DefaultRequestMultipartBody>(\n  data: string,\n  headers?: Headers,\n): T | undefined {\n  const contentType = headers?.get('content-type')\n\n  if (!contentType) {\n    return undefined\n  }\n\n  const [, ...directives] = contentType.split(/; */)\n  const boundary = directives\n    .filter((d) => d.startsWith('boundary='))\n    .map((s) => s.replace(/^boundary=/, ''))[0]\n\n  if (!boundary) {\n    return undefined\n  }\n\n  const boundaryRegExp = new RegExp(`--+${boundary}`)\n  const fields = data\n    .split(boundaryRegExp)\n    .filter((chunk) => chunk.startsWith('\\r\\n') && chunk.endsWith('\\r\\n'))\n    .map((chunk) => chunk.trimStart().replace(/\\r\\n$/, ''))\n\n  if (!fields.length) {\n    return undefined\n  }\n\n  const parsedBody: DefaultRequestMultipartBody = {}\n\n  try {\n    for (const field of fields) {\n      const [contentHeaders, ...rest] = field.split('\\r\\n\\r\\n')\n      const contentBody = rest.join('\\r\\n\\r\\n')\n      const { contentType, filename, name } =\n        parseContentHeaders(contentHeaders)\n\n      const value =\n        filename === undefined\n          ? contentBody\n          : new File([contentBody], filename, { type: contentType })\n\n      const parsedValue = parsedBody[name]\n\n      if (parsedValue === undefined) {\n        parsedBody[name] = value\n      } else if (Array.isArray(parsedValue)) {\n        parsedBody[name] = [...parsedValue, value]\n      } else {\n        parsedBody[name] = [parsedValue, value]\n      }\n    }\n\n    return parsedBody as T\n  } catch {\n    return undefined\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAAgC;AAgBhC,SAAS,oBAAoB,eAA6C;AACxE,QAAM,cAAU,yCAAgB,aAAa;AAC7C,QAAM,cAAc,QAAQ,IAAI,cAAc,KAAK;AACnD,QAAM,cAAc,QAAQ,IAAI,qBAAqB;AAErD,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC7D;AAEA,QAAM,aAAa,YAAY,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,UAAU;AAC/D,UAAM,CAACA,OAAM,GAAG,IAAI,IAAI,MAAM,KAAK,EAAE,MAAM,GAAG;AAC9C,QAAIA,KAAI,IAAI,KAAK,KAAK,GAAG;AACzB,WAAO;AAAA,EACT,GAAG,CAAC,CAAgC;AAEpC,QAAM,OAAO,WAAW,MAAM,MAAM,GAAG,EAAE;AACzC,QAAM,WAAW,WAAW,UAAU,MAAM,GAAG,EAAE;AAEjD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAMO,SAAS,mBACd,MACA,SACe;AACf,QAAM,cAAc,SAAS,IAAI,cAAc;AAE/C,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,CAAC,EAAE,GAAG,UAAU,IAAI,YAAY,MAAM,KAAK;AACjD,QAAM,WAAW,WACd,OAAO,CAAC,MAAM,EAAE,WAAW,WAAW,CAAC,EACvC,IAAI,CAAC,MAAM,EAAE,QAAQ,cAAc,EAAE,CAAC,EAAE,CAAC;AAE5C,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAEA,QAAM,iBAAiB,IAAI,OAAO,MAAM,QAAQ,EAAE;AAClD,QAAM,SAAS,KACZ,MAAM,cAAc,EACpB,OAAO,CAAC,UAAU,MAAM,WAAW,MAAM,KAAK,MAAM,SAAS,MAAM,CAAC,EACpE,IAAI,CAAC,UAAU,MAAM,UAAU,EAAE,QAAQ,SAAS,EAAE,CAAC;AAExD,MAAI,CAAC,OAAO,QAAQ;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,aAA0C,CAAC;AAEjD,MAAI;AACF,eAAW,SAAS,QAAQ;AAC1B,YAAM,CAAC,gBAAgB,GAAG,IAAI,IAAI,MAAM,MAAM,UAAU;AACxD,YAAM,cAAc,KAAK,KAAK,UAAU;AACxC,YAAM,EAAE,aAAAC,cAAa,UAAU,KAAK,IAClC,oBAAoB,cAAc;AAEpC,YAAM,QACJ,aAAa,SACT,cACA,IAAI,KAAK,CAAC,WAAW,GAAG,UAAU,EAAE,MAAMA,aAAY,CAAC;AAE7D,YAAM,cAAc,WAAW,IAAI;AAEnC,UAAI,gBAAgB,QAAW;AAC7B,mBAAW,IAAI,IAAI;AAAA,MACrB,WAAW,MAAM,QAAQ,WAAW,GAAG;AACrC,mBAAW,IAAI,IAAI,CAAC,GAAG,aAAa,KAAK;AAAA,MAC3C,OAAO;AACL,mBAAW,IAAI,IAAI,CAAC,aAAa,KAAK;AAAA,MACxC;AAAA,IACF;AAEA,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF;", "names": ["name", "contentType"]}