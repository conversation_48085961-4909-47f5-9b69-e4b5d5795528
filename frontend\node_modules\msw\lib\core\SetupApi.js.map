{"version": 3, "sources": ["../../src/core/SetupApi.ts"], "sourcesContent": ["import { invariant } from 'outvariant'\nimport { EventMap, Emitter } from 'strict-event-emitter'\nimport { RequestHandler } from './handlers/RequestHandler'\nimport { LifeCycleEventEmitter } from './sharedOptions'\nimport { devUtils } from './utils/internal/devUtils'\nimport { pipeEvents } from './utils/internal/pipeEvents'\nimport { toReadonlyArray } from './utils/internal/toReadonlyArray'\nimport { Disposable } from './utils/internal/Disposable'\nimport type { WebSocketHandler } from './handlers/WebSocketHandler'\n\nexport abstract class HandlersController {\n  abstract prepend(\n    runtimeHandlers: Array<RequestHandler | WebSocketHandler>,\n  ): void\n  abstract reset(nextHandles: Array<RequestHandler | WebSocketHandler>): void\n  abstract currentHandlers(): Array<RequestHandler | WebSocketHandler>\n}\n\nexport class InMemoryHandlersController implements HandlersController {\n  private handlers: Array<RequestHandler | WebSocketHandler>\n\n  constructor(\n    private initialHandlers: Array<RequestHandler | WebSocketHandler>,\n  ) {\n    this.handlers = [...initialHandlers]\n  }\n\n  public prepend(\n    runtimeHandles: Array<RequestHandler | WebSocketHandler>,\n  ): void {\n    this.handlers.unshift(...runtimeHandles)\n  }\n\n  public reset(nextHandlers: Array<RequestHandler | WebSocketHandler>): void {\n    this.handlers =\n      nextHandlers.length > 0 ? [...nextHandlers] : [...this.initialHandlers]\n  }\n\n  public currentHandlers(): Array<RequestHandler | WebSocketHandler> {\n    return this.handlers\n  }\n}\n\n/**\n * Generic class for the mock API setup.\n */\nexport abstract class SetupApi<EventsMap extends EventMap> extends Disposable {\n  protected handlersController: HandlersController\n  protected readonly emitter: Emitter<EventsMap>\n  protected readonly publicEmitter: Emitter<EventsMap>\n\n  public readonly events: LifeCycleEventEmitter<EventsMap>\n\n  constructor(...initialHandlers: Array<RequestHandler | WebSocketHandler>) {\n    super()\n\n    invariant(\n      this.validateHandlers(initialHandlers),\n      devUtils.formatMessage(\n        `Failed to apply given request handlers: invalid input. Did you forget to spread the request handlers Array?`,\n      ),\n    )\n\n    this.handlersController = new InMemoryHandlersController(initialHandlers)\n\n    this.emitter = new Emitter<EventsMap>()\n    this.publicEmitter = new Emitter<EventsMap>()\n    pipeEvents(this.emitter, this.publicEmitter)\n\n    this.events = this.createLifeCycleEvents()\n\n    this.subscriptions.push(() => {\n      this.emitter.removeAllListeners()\n      this.publicEmitter.removeAllListeners()\n    })\n  }\n\n  private validateHandlers(handlers: ReadonlyArray<unknown>): boolean {\n    // Guard against incorrect call signature of the setup API.\n    return handlers.every((handler) => !Array.isArray(handler))\n  }\n\n  public use(\n    ...runtimeHandlers: Array<RequestHandler | WebSocketHandler>\n  ): void {\n    invariant(\n      this.validateHandlers(runtimeHandlers),\n      devUtils.formatMessage(\n        `Failed to call \"use()\" with the given request handlers: invalid input. Did you forget to spread the array of request handlers?`,\n      ),\n    )\n\n    this.handlersController.prepend(runtimeHandlers)\n  }\n\n  public restoreHandlers(): void {\n    this.handlersController.currentHandlers().forEach((handler) => {\n      if ('isUsed' in handler) {\n        handler.isUsed = false\n      }\n    })\n  }\n\n  public resetHandlers(\n    ...nextHandlers: Array<RequestHandler | WebSocketHandler>\n  ): void {\n    this.handlersController.reset(nextHandlers)\n  }\n\n  public listHandlers(): ReadonlyArray<RequestHandler | WebSocketHandler> {\n    return toReadonlyArray(this.handlersController.currentHandlers())\n  }\n\n  private createLifeCycleEvents(): LifeCycleEventEmitter<EventsMap> {\n    return {\n      on: (...args: any[]) => {\n        return (this.publicEmitter.on as any)(...args)\n      },\n      removeListener: (...args: any[]) => {\n        return (this.publicEmitter.removeListener as any)(...args)\n      },\n      removeAllListeners: (...args: any[]) => {\n        return this.publicEmitter.removeAllListeners(...args)\n      },\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAA0B;AAC1B,kCAAkC;AAGlC,sBAAyB;AACzB,wBAA2B;AAC3B,6BAAgC;AAChC,wBAA2B;AAGpB,MAAe,mBAAmB;AAMzC;AAEO,MAAM,2BAAyD;AAAA,EAGpE,YACU,iBACR;AADQ;AAER,SAAK,WAAW,CAAC,GAAG,eAAe;AAAA,EACrC;AAAA,EANQ;AAAA,EAQD,QACL,gBACM;AACN,SAAK,SAAS,QAAQ,GAAG,cAAc;AAAA,EACzC;AAAA,EAEO,MAAM,cAA8D;AACzE,SAAK,WACH,aAAa,SAAS,IAAI,CAAC,GAAG,YAAY,IAAI,CAAC,GAAG,KAAK,eAAe;AAAA,EAC1E;AAAA,EAEO,kBAA4D;AACjE,WAAO,KAAK;AAAA,EACd;AACF;AAKO,MAAe,iBAA6C,6BAAW;AAAA,EAClE;AAAA,EACS;AAAA,EACA;AAAA,EAEH;AAAA,EAEhB,eAAe,iBAA2D;AACxE,UAAM;AAEN;AAAA,MACE,KAAK,iBAAiB,eAAe;AAAA,MACrC,yBAAS;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,SAAK,qBAAqB,IAAI,2BAA2B,eAAe;AAExE,SAAK,UAAU,IAAI,oCAAmB;AACtC,SAAK,gBAAgB,IAAI,oCAAmB;AAC5C,sCAAW,KAAK,SAAS,KAAK,aAAa;AAE3C,SAAK,SAAS,KAAK,sBAAsB;AAEzC,SAAK,cAAc,KAAK,MAAM;AAC5B,WAAK,QAAQ,mBAAmB;AAChC,WAAK,cAAc,mBAAmB;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EAEQ,iBAAiB,UAA2C;AAElE,WAAO,SAAS,MAAM,CAAC,YAAY,CAAC,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC5D;AAAA,EAEO,OACF,iBACG;AACN;AAAA,MACE,KAAK,iBAAiB,eAAe;AAAA,MACrC,yBAAS;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,SAAK,mBAAmB,QAAQ,eAAe;AAAA,EACjD;AAAA,EAEO,kBAAwB;AAC7B,SAAK,mBAAmB,gBAAgB,EAAE,QAAQ,CAAC,YAAY;AAC7D,UAAI,YAAY,SAAS;AACvB,gBAAQ,SAAS;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,iBACF,cACG;AACN,SAAK,mBAAmB,MAAM,YAAY;AAAA,EAC5C;AAAA,EAEO,eAAiE;AACtE,eAAO,wCAAgB,KAAK,mBAAmB,gBAAgB,CAAC;AAAA,EAClE;AAAA,EAEQ,wBAA0D;AAChE,WAAO;AAAA,MACL,IAAI,IAAI,SAAgB;AACtB,eAAQ,KAAK,cAAc,GAAW,GAAG,IAAI;AAAA,MAC/C;AAAA,MACA,gBAAgB,IAAI,SAAgB;AAClC,eAAQ,KAAK,cAAc,eAAuB,GAAG,IAAI;AAAA,MAC3D;AAAA,MACA,oBAAoB,IAAI,SAAgB;AACtC,eAAO,KAAK,cAAc,mBAAmB,GAAG,IAAI;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AACF;", "names": []}