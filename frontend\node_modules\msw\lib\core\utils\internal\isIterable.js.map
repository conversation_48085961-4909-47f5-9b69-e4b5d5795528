{"version": 3, "sources": ["../../../../src/core/utils/internal/isIterable.ts"], "sourcesContent": ["/**\n * This is the same as TypeScript's `Iterable`, but with all three type parameters.\n * @todo Remove once TypeScript 5.6 is the minimum.\n */\nexport interface Iterable<T, TReturn, TNext> {\n  [Symbol.iterator](): Iterator<T, TReturn, TNext>\n}\n\n/**\n * This is the same as TypeScript's `AsyncIterable`, but with all three type parameters.\n * @todo Remove once TypeScript 5.6 is the minimum.\n */\nexport interface AsyncIterable<T, TReturn, TNext> {\n  [Symbol.asyncIterator](): AsyncIterator<T, TReturn, TNext>\n}\n\n/**\n * Determines if the given function is an iterator.\n */\nexport function isIterable<IteratorType>(\n  fn: any,\n): fn is\n  | Iterable<IteratorType, IteratorType, IteratorType>\n  | AsyncIterable<IteratorType, IteratorType, IteratorType> {\n  if (!fn) {\n    return false\n  }\n\n  return (\n    Reflect.has(fn, Symbol.iterator) || Reflect.has(fn, Symbol.asyncIterator)\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBO,SAAS,WACd,IAG0D;AAC1D,MAAI,CAAC,IAAI;AACP,WAAO;AAAA,EACT;AAEA,SACE,QAAQ,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,IAAI,OAAO,aAAa;AAE5E;", "names": []}