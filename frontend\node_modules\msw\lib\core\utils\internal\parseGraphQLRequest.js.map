{"version": 3, "sources": ["../../../../src/core/utils/internal/parseGraphQLRequest.ts"], "sourcesContent": ["import type {\n  DocumentNode,\n  OperationDefinitionNode,\n  OperationTypeNode,\n} from 'graphql'\nimport type { GraphQLVariables } from '../../handlers/GraphQLHandler'\nimport { toPublicUrl } from '../request/toPublicUrl'\nimport { devUtils } from './devUtils'\nimport { jsonParse } from './jsonParse'\nimport { parseMultipartData } from './parseMultipartData'\n\ninterface GraphQLInput {\n  query: string | null\n  variables?: GraphQLVariables\n}\n\nexport interface ParsedGraphQLQuery {\n  operationType: OperationTypeNode\n  operationName?: string\n}\n\nexport type ParsedGraphQLRequest<\n  VariablesType extends GraphQLVariables = GraphQLVariables,\n> =\n  | (ParsedGraphQLQuery & {\n      query: string\n      variables?: VariablesType\n    })\n  | undefined\n\nexport function parseDocumentNode(node: DocumentNode): ParsedGraphQLQuery {\n  const operationDef = node.definitions.find((definition) => {\n    return definition.kind === 'OperationDefinition'\n  }) as OperationDefinitionNode\n\n  return {\n    operationType: operationDef?.operation,\n    operationName: operationDef?.name?.value,\n  }\n}\n\nasync function parseQuery(query: string): Promise<ParsedGraphQLQuery | Error> {\n  /**\n   * @note Use `require` to get the \"graphql\" module here.\n   * It has to be scoped to this function because this module leaks to the\n   * root export. It has to be `require` because tools like Jest have trouble\n   * handling dynamic imports. It gets replaced with a dynamic import on build time.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { parse } = require('graphql')\n\n  try {\n    const ast = parse(query)\n    return parseDocumentNode(ast)\n  } catch (error) {\n    return error as Error\n  }\n}\n\nexport type GraphQLParsedOperationsMap = Record<string, string[]>\nexport type GraphQLMultipartRequestBody = {\n  operations: string\n  map?: string\n} & {\n  [fileName: string]: File\n}\n\nfunction extractMultipartVariables<VariablesType extends GraphQLVariables>(\n  variables: VariablesType,\n  map: GraphQLParsedOperationsMap,\n  files: Record<string, File>,\n) {\n  const operations = { variables }\n\n  for (const [key, pathArray] of Object.entries(map)) {\n    if (!(key in files)) {\n      throw new Error(`Given files do not have a key '${key}' .`)\n    }\n\n    for (const dotPath of pathArray) {\n      const [lastPath, ...reversedPaths] = dotPath.split('.').reverse()\n      const paths = reversedPaths.reverse()\n      let target: Record<string, any> = operations\n\n      for (const path of paths) {\n        if (!(path in target)) {\n          throw new Error(`Property '${paths}' is not in operations.`)\n        }\n\n        target = target[path]\n      }\n\n      target[lastPath] = files[key]\n    }\n  }\n\n  return operations.variables\n}\n\nasync function getGraphQLInput(request: Request): Promise<GraphQLInput | null> {\n  switch (request.method) {\n    case 'GET': {\n      const url = new URL(request.url)\n      const query = url.searchParams.get('query')\n      const variables = url.searchParams.get('variables') || ''\n\n      return {\n        query,\n        variables: jsonParse(variables),\n      }\n    }\n\n    case 'POST': {\n      // Clone the request so we could read its body without locking\n      // the body stream to the downward consumers.\n      const requestClone = request.clone()\n\n      // Handle multipart body GraphQL operations.\n      if (\n        request.headers.get('content-type')?.includes('multipart/form-data')\n      ) {\n        const responseJson = parseMultipartData<GraphQLMultipartRequestBody>(\n          await requestClone.text(),\n          request.headers,\n        )\n\n        if (!responseJson) {\n          return null\n        }\n\n        const { operations, map, ...files } = responseJson\n        const parsedOperations =\n          jsonParse<{ query?: string; variables?: GraphQLVariables }>(\n            operations,\n          ) || {}\n\n        if (!parsedOperations.query) {\n          return null\n        }\n\n        const parsedMap = jsonParse<GraphQLParsedOperationsMap>(map || '') || {}\n        const variables = parsedOperations.variables\n          ? extractMultipartVariables(\n              parsedOperations.variables,\n              parsedMap,\n              files,\n            )\n          : {}\n\n        return {\n          query: parsedOperations.query,\n          variables,\n        }\n      }\n\n      // Handle plain POST GraphQL operations.\n      const requestJson: {\n        query: string\n        variables?: GraphQLVariables\n        operations?: any /** @todo Annotate this */\n      } = await requestClone.json().catch(() => null)\n\n      if (requestJson?.query) {\n        const { query, variables } = requestJson\n\n        return {\n          query,\n          variables,\n        }\n      }\n    }\n\n    default:\n      return null\n  }\n}\n\n/**\n * Determines if a given request can be considered a GraphQL request.\n * Does not parse the query and does not guarantee its validity.\n */\nexport async function parseGraphQLRequest(\n  request: Request,\n): Promise<ParsedGraphQLRequest> {\n  const input = await getGraphQLInput(request)\n\n  if (!input || !input.query) {\n    return\n  }\n\n  const { query, variables } = input\n  const parsedResult = await parseQuery(query)\n\n  if (parsedResult instanceof Error) {\n    const requestPublicUrl = toPublicUrl(request.url)\n\n    throw new Error(\n      devUtils.formatMessage(\n        'Failed to intercept a GraphQL request to \"%s %s\": cannot parse query. See the error message from the parser below.\\n\\n%s',\n        request.method,\n        requestPublicUrl,\n        parsedResult.message,\n      ),\n    )\n  }\n\n  return {\n    query: input.query,\n    operationType: parsedResult.operationType,\n    operationName: parsedResult.operationName,\n    variables,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,yBAA4B;AAC5B,sBAAyB;AACzB,uBAA0B;AAC1B,gCAAmC;AAqB5B,SAAS,kBAAkB,MAAwC;AACxE,QAAM,eAAe,KAAK,YAAY,KAAK,CAAC,eAAe;AACzD,WAAO,WAAW,SAAS;AAAA,EAC7B,CAAC;AAED,SAAO;AAAA,IACL,eAAe,cAAc;AAAA,IAC7B,eAAe,cAAc,MAAM;AAAA,EACrC;AACF;AAEA,eAAe,WAAW,OAAoD;AAQ5E,QAAM,EAAE,MAAM,IAAI,QAAQ,SAAS;AAEnC,MAAI;AACF,UAAM,MAAM,MAAM,KAAK;AACvB,WAAO,kBAAkB,GAAG;AAAA,EAC9B,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AACF;AAUA,SAAS,0BACP,WACA,KACA,OACA;AACA,QAAM,aAAa,EAAE,UAAU;AAE/B,aAAW,CAAC,KAAK,SAAS,KAAK,OAAO,QAAQ,GAAG,GAAG;AAClD,QAAI,EAAE,OAAO,QAAQ;AACnB,YAAM,IAAI,MAAM,kCAAkC,GAAG,KAAK;AAAA,IAC5D;AAEA,eAAW,WAAW,WAAW;AAC/B,YAAM,CAAC,UAAU,GAAG,aAAa,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ;AAChE,YAAM,QAAQ,cAAc,QAAQ;AACpC,UAAI,SAA8B;AAElC,iBAAW,QAAQ,OAAO;AACxB,YAAI,EAAE,QAAQ,SAAS;AACrB,gBAAM,IAAI,MAAM,aAAa,KAAK,yBAAyB;AAAA,QAC7D;AAEA,iBAAS,OAAO,IAAI;AAAA,MACtB;AAEA,aAAO,QAAQ,IAAI,MAAM,GAAG;AAAA,IAC9B;AAAA,EACF;AAEA,SAAO,WAAW;AACpB;AAEA,eAAe,gBAAgB,SAAgD;AAC7E,UAAQ,QAAQ,QAAQ;AAAA,IACtB,KAAK,OAAO;AACV,YAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAM,QAAQ,IAAI,aAAa,IAAI,OAAO;AAC1C,YAAM,YAAY,IAAI,aAAa,IAAI,WAAW,KAAK;AAEvD,aAAO;AAAA,QACL;AAAA,QACA,eAAW,4BAAU,SAAS;AAAA,MAChC;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AAGX,YAAM,eAAe,QAAQ,MAAM;AAGnC,UACE,QAAQ,QAAQ,IAAI,cAAc,GAAG,SAAS,qBAAqB,GACnE;AACA,cAAM,mBAAe;AAAA,UACnB,MAAM,aAAa,KAAK;AAAA,UACxB,QAAQ;AAAA,QACV;AAEA,YAAI,CAAC,cAAc;AACjB,iBAAO;AAAA,QACT;AAEA,cAAM,EAAE,YAAY,KAAK,GAAG,MAAM,IAAI;AACtC,cAAM,uBACJ;AAAA,UACE;AAAA,QACF,KAAK,CAAC;AAER,YAAI,CAAC,iBAAiB,OAAO;AAC3B,iBAAO;AAAA,QACT;AAEA,cAAM,gBAAY,4BAAsC,OAAO,EAAE,KAAK,CAAC;AACvE,cAAM,YAAY,iBAAiB,YAC/B;AAAA,UACE,iBAAiB;AAAA,UACjB;AAAA,UACA;AAAA,QACF,IACA,CAAC;AAEL,eAAO;AAAA,UACL,OAAO,iBAAiB;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAGA,YAAM,cAIF,MAAM,aAAa,KAAK,EAAE,MAAM,MAAM,IAAI;AAE9C,UAAI,aAAa,OAAO;AACtB,cAAM,EAAE,OAAO,UAAU,IAAI;AAE7B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA;AACE,aAAO;AAAA,EACX;AACF;AAMA,eAAsB,oBACpB,SAC+B;AAC/B,QAAM,QAAQ,MAAM,gBAAgB,OAAO;AAE3C,MAAI,CAAC,SAAS,CAAC,MAAM,OAAO;AAC1B;AAAA,EACF;AAEA,QAAM,EAAE,OAAO,UAAU,IAAI;AAC7B,QAAM,eAAe,MAAM,WAAW,KAAK;AAE3C,MAAI,wBAAwB,OAAO;AACjC,UAAM,uBAAmB,gCAAY,QAAQ,GAAG;AAEhD,UAAM,IAAI;AAAA,MACR,yBAAS;AAAA,QACP;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,OAAO,MAAM;AAAA,IACb,eAAe,aAAa;AAAA,IAC5B,eAAe,aAAa;AAAA,IAC5B;AAAA,EACF;AACF;", "names": []}