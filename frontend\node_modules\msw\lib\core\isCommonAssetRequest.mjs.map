{"version": 3, "sources": ["../../src/core/isCommonAssetRequest.ts"], "sourcesContent": ["/**\n * Determines if the given request is a static asset request.\n * Useful when deciding which unhandled requests to ignore.\n * @note Despite being ignored, you can still intercept and mock\n * static assets by creating request handlers for them.\n *\n * @example\n * import { isCommonAssetRequest } from 'msw'\n *\n * await worker.start({\n *   onUnhandledRequest(request, print) {\n *     if (!isCommonAssetRequest(request)) {\n *       print.warning()\n *     }\n *   }\n * })\n */\nexport function isCommonAssetRequest(request: Request): boolean {\n  const url = new URL(request.url)\n\n  // Ignore certain protocols.\n  if (url.protocol === 'file:') {\n    return true\n  }\n\n  // Ignore static assets hosts.\n  if (/(fonts\\.googleapis\\.com)/.test(url.hostname)) {\n    return true\n  }\n\n  // Ignore node modules served over HTTP.\n  if (/node_modules/.test(url.pathname)) {\n    return true\n  }\n\n  // Ignore internal Vite requests, like \"/@vite/client\".\n  if (url.pathname.includes('@vite')) {\n    return true\n  }\n\n  // Ignore common static assets.\n  return /\\.(s?css|less|m?jsx?|m?tsx?|html|ttf|otf|woff|woff2|eot|gif|jpe?g|png|avif|webp|svg|mp4|webm|ogg|mov|mp3|wav|ogg|flac|aac|pdf|txt|csv|json|xml|md|zip|tar|gz|rar|7z)$/i.test(\n    url.pathname,\n  )\n}\n"], "mappings": "AAiBO,SAAS,qBAAqB,SAA2B;AAC9D,QAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAG/B,MAAI,IAAI,aAAa,SAAS;AAC5B,WAAO;AAAA,EACT;AAGA,MAAI,2BAA2B,KAAK,IAAI,QAAQ,GAAG;AACjD,WAAO;AAAA,EACT;AAGA,MAAI,eAAe,KAAK,IAAI,QAAQ,GAAG;AACrC,WAAO;AAAA,EACT;AAGA,MAAI,IAAI,SAAS,SAAS,OAAO,GAAG;AAClC,WAAO;AAAA,EACT;AAGA,SAAO,yKAAyK;AAAA,IAC9K,IAAI;AAAA,EACN;AACF;", "names": []}