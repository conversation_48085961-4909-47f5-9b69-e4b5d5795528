{"version": 3, "sources": ["../../../../src/core/utils/logging/serializeRequest.ts"], "sourcesContent": ["export interface LoggedRequest {\n  url: URL\n  method: string\n  headers: Record<string, string>\n  body: string\n}\n\n/**\n * Formats a mocked request for introspection in browser's console.\n */\nexport async function serializeRequest(\n  request: Request,\n): Promise<LoggedRequest> {\n  const requestClone = request.clone()\n  const requestText = await requestClone.text()\n\n  return {\n    url: new URL(request.url),\n    method: request.method,\n    headers: Object.fromEntries(request.headers.entries()),\n    body: requestText,\n  }\n}\n"], "mappings": "AAUA,eAAsB,iBACpB,SACwB;AACxB,QAAM,eAAe,QAAQ,MAAM;AACnC,QAAM,cAAc,MAAM,aAAa,KAAK;AAE5C,SAAO;AAAA,IACL,KAAK,IAAI,IAAI,QAAQ,GAAG;AAAA,IACxB,QAAQ,QAAQ;AAAA,IAChB,SAAS,OAAO,YAAY,QAAQ,QAAQ,QAAQ,CAAC;AAAA,IACrD,MAAM;AAAA,EACR;AACF;", "names": []}