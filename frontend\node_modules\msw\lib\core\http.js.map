{"version": 3, "sources": ["../../src/core/http.ts"], "sourcesContent": ["import {\n  DefaultBodyType,\n  RequestHandlerOptions,\n  ResponseResolver,\n} from './handlers/RequestHandler'\nimport {\n  HttpMethods,\n  HttpHandler,\n  HttpRequestResolverExtras,\n} from './handlers/HttpHandler'\nimport type { Path, PathParams } from './utils/matching/matchRequestUrl'\n\nexport type HttpRequestHandler = <\n  Params extends PathParams<keyof Params> = PathParams,\n  RequestBodyType extends DefaultBodyType = DefaultBodyType,\n  // Response body type MUST be undefined by default.\n  // This is how we can distinguish between a handler that\n  // returns plain \"Response\" and the one returning \"HttpResponse\"\n  // to enforce a stricter response body type.\n  ResponseBodyType extends DefaultBodyType = undefined,\n  RequestPath extends Path = Path,\n>(\n  path: RequestPath,\n  resolver: HttpResponseResolver<Params, RequestBodyType, ResponseBodyType>,\n  options?: RequestHandlerOptions,\n) => HttpHandler\n\nexport type HttpResponseResolver<\n  Params extends PathParams<keyof Params> = PathParams,\n  RequestBodyType extends DefaultBodyType = DefaultBodyType,\n  ResponseBodyType extends DefaultBodyType = DefaultBodyType,\n> = ResponseResolver<\n  HttpRequestResolverExtras<Params>,\n  RequestBodyType,\n  ResponseBodyType\n>\n\nfunction createHttpHandler<Method extends HttpMethods | RegExp>(\n  method: Method,\n): HttpRequestHandler {\n  return (path, resolver, options = {}) => {\n    return new HttpHandler(method, path, resolver, options)\n  }\n}\n\n/**\n * A namespace to intercept and mock HTTP requests.\n *\n * @example\n * http.get('/user', resolver)\n * http.post('/post/:id', resolver)\n *\n * @see {@link https://mswjs.io/docs/api/http `http` API reference}\n */\nexport const http = {\n  all: createHttpHandler(/.+/),\n  head: createHttpHandler(HttpMethods.HEAD),\n  get: createHttpHandler(HttpMethods.GET),\n  post: createHttpHandler(HttpMethods.POST),\n  put: createHttpHandler(HttpMethods.PUT),\n  delete: createHttpHandler(HttpMethods.DELETE),\n  patch: createHttpHandler(HttpMethods.PATCH),\n  options: createHttpHandler(HttpMethods.OPTIONS),\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,yBAIO;AA4BP,SAAS,kBACP,QACoB;AACpB,SAAO,CAAC,MAAM,UAAU,UAAU,CAAC,MAAM;AACvC,WAAO,IAAI,+BAAY,QAAQ,MAAM,UAAU,OAAO;AAAA,EACxD;AACF;AAWO,MAAM,OAAO;AAAA,EAClB,KAAK,kBAAkB,IAAI;AAAA,EAC3B,MAAM,kBAAkB,+BAAY,IAAI;AAAA,EACxC,KAAK,kBAAkB,+BAAY,GAAG;AAAA,EACtC,MAAM,kBAAkB,+BAAY,IAAI;AAAA,EACxC,KAAK,kBAAkB,+BAAY,GAAG;AAAA,EACtC,QAAQ,kBAAkB,+BAAY,MAAM;AAAA,EAC5C,OAAO,kBAAkB,+BAAY,KAAK;AAAA,EAC1C,SAAS,kBAAkB,+BAAY,OAAO;AAChD;", "names": []}