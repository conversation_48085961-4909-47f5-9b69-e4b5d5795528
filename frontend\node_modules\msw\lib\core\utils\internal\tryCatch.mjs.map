{"version": 3, "sources": ["../../../../src/core/utils/internal/tryCatch.ts"], "sourcesContent": ["export function tryCatch<Fn extends (...args: any[]) => any>(\n  fn: Fn,\n  onException?: (error: Error) => void,\n): ReturnType<Fn> | undefined {\n  try {\n    const result = fn()\n    return result\n  } catch (error) {\n    onException?.(error as Error)\n  }\n}\n"], "mappings": "AAAO,SAAS,SACd,IACA,aAC4B;AAC5B,MAAI;AACF,UAAM,SAAS,GAAG;AAClB,WAAO;AAAA,EACT,SAAS,OAAO;AACd,kBAAc,KAAc;AAAA,EAC9B;AACF;", "names": []}