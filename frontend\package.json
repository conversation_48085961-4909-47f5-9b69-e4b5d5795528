{"name": "resume‑matcher‑frontend", "type": "module", "scripts": {"dev": "vite"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.515.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.23.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.5", "shadcn": "^2.6.3", "tailwindcss": "^3.4.0", "tw-animate-css": "^1.3.4", "vite": "^5.2.0"}}