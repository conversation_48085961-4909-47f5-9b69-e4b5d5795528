{"version": 3, "sources": ["../../src/core/graphql.ts"], "sourcesContent": ["import type { DocumentNode, OperationTypeNode } from 'graphql'\nimport {\n  ResponseResolver,\n  RequestHandlerOptions,\n} from './handlers/RequestHandler'\nimport {\n  GraphQLHandler,\n  GraphQLVariables,\n  ExpectedOperationTypeNode,\n  GraphQLHandlerNameSelector,\n  GraphQLResolverExtras,\n  GraphQLResponseBody,\n  GraphQLQuery,\n} from './handlers/GraphQLHandler'\nimport type { Path } from './utils/matching/matchRequestUrl'\n\nexport interface TypedDocumentNode<\n  Result = { [key: string]: any },\n  Variables = { [key: string]: any },\n> extends DocumentNode {\n  __apiType?: (variables: Variables) => Result\n  __resultType?: Result\n  __variablesType?: Variables\n}\n\nexport type GraphQLRequestHandler = <\n  Query extends GraphQLQuery = GraphQLQuery,\n  Variables extends GraphQLVariables = GraphQLVariables,\n>(\n  operationName:\n    | GraphQLHandlerNameSelector\n    | DocumentNode\n    | TypedDocumentNode<Query, Variables>,\n  resolver: GraphQLResponseResolver<\n    [Query] extends [never] ? GraphQLQuery : Query,\n    Variables\n  >,\n  options?: RequestHandlerOptions,\n) => GraphQLHandler\n\nexport type GraphQLResponseResolver<\n  Query extends GraphQLQuery = GraphQLQuery,\n  Variables extends GraphQLVariables = GraphQLVariables,\n> = ResponseResolver<\n  GraphQLResolverExtras<Variables>,\n  null,\n  GraphQLResponseBody<[Query] extends [never] ? GraphQLQuery : Query>\n>\n\nfunction createScopedGraphQLHandler(\n  operationType: ExpectedOperationTypeNode,\n  url: Path,\n): GraphQLRequestHandler {\n  return (operationName, resolver, options = {}) => {\n    return new GraphQLHandler(\n      operationType,\n      operationName,\n      url,\n      resolver,\n      options,\n    )\n  }\n}\n\nfunction createGraphQLOperationHandler(url: Path) {\n  return <\n    Query extends GraphQLQuery = GraphQLQuery,\n    Variables extends GraphQLVariables = GraphQLVariables,\n  >(\n    resolver: ResponseResolver<\n      GraphQLResolverExtras<Variables>,\n      null,\n      GraphQLResponseBody<Query>\n    >,\n  ) => {\n    return new GraphQLHandler('all', new RegExp('.*'), url, resolver)\n  }\n}\n\nconst standardGraphQLHandlers = {\n  /**\n   * Intercepts a GraphQL query by a given name.\n   *\n   * @example\n   * graphql.query('GetUser', () => {\n   *   return HttpResponse.json({ data: { user: { name: 'John' } } })\n   * })\n   *\n   * @see {@link https://mswjs.io/docs/api/graphql#graphqlqueryqueryname-resolver `graphql.query()` API reference}\n   */\n  query: createScopedGraphQLHandler('query' as OperationTypeNode, '*'),\n\n  /**\n   * Intercepts a GraphQL mutation by its name.\n   *\n   * @example\n   * graphql.mutation('SavePost', () => {\n   *   return HttpResponse.json({ data: { post: { id: 'abc-123 } } })\n   * })\n   *\n   * @see {@link https://mswjs.io/docs/api/graphql#graphqlmutationmutationname-resolver `graphql.query()` API reference}\n   *\n   */\n  mutation: createScopedGraphQLHandler('mutation' as OperationTypeNode, '*'),\n\n  /**\n   * Intercepts any GraphQL operation, regardless of its type or name.\n   *\n   * @example\n   * graphql.operation(() => {\n   *   return HttpResponse.json({ data: { name: 'John' } })\n   * })\n   *\n   * @see {@link https://mswjs.io/docs/api/graphql#graphqloperationresolver `graphql.operation()` API reference}\n   */\n  operation: createGraphQLOperationHandler('*'),\n}\n\nfunction createGraphQLLink(url: Path): typeof standardGraphQLHandlers {\n  return {\n    operation: createGraphQLOperationHandler(url),\n    query: createScopedGraphQLHandler('query' as OperationTypeNode, url),\n    mutation: createScopedGraphQLHandler('mutation' as OperationTypeNode, url),\n  }\n}\n\n/**\n * A namespace to intercept and mock GraphQL operations\n *\n * @example\n * graphql.query('GetUser', resolver)\n * graphql.mutation('DeletePost', resolver)\n *\n * @see {@link https://mswjs.io/docs/api/graphql `graphql` API reference}\n */\nexport const graphql = {\n  ...standardGraphQLHandlers,\n\n  /**\n   * Intercepts GraphQL operations scoped by the given URL.\n   *\n   * @example\n   * const github = graphql.link('https://api.github.com/graphql')\n   * github.query('GetRepo', resolver)\n   *\n   * @see {@link https://mswjs.io/docs/api/graphql#graphqllinkurl `graphql.link()` API reference}\n   */\n  link: createGraphQLLink,\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,4BAQO;AAoCP,SAAS,2BACP,eACA,KACuB;AACvB,SAAO,CAAC,eAAe,UAAU,UAAU,CAAC,MAAM;AAChD,WAAO,IAAI;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,KAAW;AAChD,SAAO,CAIL,aAKG;AACH,WAAO,IAAI,qCAAe,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK,QAAQ;AAAA,EAClE;AACF;AAEA,MAAM,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAW9B,OAAO,2BAA2B,SAA8B,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAanE,UAAU,2BAA2B,YAAiC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYzE,WAAW,8BAA8B,GAAG;AAC9C;AAEA,SAAS,kBAAkB,KAA2C;AACpE,SAAO;AAAA,IACL,WAAW,8BAA8B,GAAG;AAAA,IAC5C,OAAO,2BAA2B,SAA8B,GAAG;AAAA,IACnE,UAAU,2BAA2B,YAAiC,GAAG;AAAA,EAC3E;AACF;AAWO,MAAM,UAAU;AAAA,EACrB,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWH,MAAM;AACR;", "names": []}