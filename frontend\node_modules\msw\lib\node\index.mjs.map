{"version": 3, "sources": ["../../src/node/SetupServerApi.ts", "../../src/node/SetupServerCommonApi.ts", "../../src/node/setupServer.ts"], "sourcesContent": ["import { AsyncLocalStorage } from 'node:async_hooks'\nimport type { HttpRequestEventMap, Interceptor } from '@mswjs/interceptors'\nimport { ClientRequestInterceptor } from '@mswjs/interceptors/ClientRequest'\nimport { XMLHttpRequestInterceptor } from '@mswjs/interceptors/XMLHttpRequest'\nimport { FetchInterceptor } from '@mswjs/interceptors/fetch'\nimport { HandlersController } from '~/core/SetupApi'\nimport type { RequestHandler } from '~/core/handlers/RequestHandler'\nimport type { WebSocketHandler } from '~/core/handlers/WebSocketHandler'\nimport type { SetupServer } from './glossary'\nimport { SetupServerCommonApi } from './SetupServerCommonApi'\n\nconst store = new AsyncLocalStorage<RequestHandlersContext>()\n\ntype RequestHandlersContext = {\n  initialHandlers: Array<RequestHandler | WebSocketHandler>\n  handlers: Array<RequestHandler | WebSocketHandler>\n}\n\n/**\n * A handlers controller that utilizes `AsyncLocalStorage` in Node.js\n * to prevent the request handlers list from being a shared state\n * across mutliple tests.\n */\nclass AsyncHandlersController implements HandlersController {\n  private rootContext: RequestHandlersContext\n\n  constructor(initialHandlers: Array<RequestHandler | WebSocketHandler>) {\n    this.rootContext = { initialHandlers, handlers: [] }\n  }\n\n  get context(): RequestHandlersContext {\n    return store.getStore() || this.rootContext\n  }\n\n  public prepend(runtimeHandlers: Array<RequestHandler | WebSocketHandler>) {\n    this.context.handlers.unshift(...runtimeHandlers)\n  }\n\n  public reset(nextHandlers: Array<RequestHandler | WebSocketHandler>) {\n    const context = this.context\n    context.handlers = []\n    context.initialHandlers =\n      nextHandlers.length > 0 ? nextHandlers : context.initialHandlers\n  }\n\n  public currentHandlers(): Array<RequestHandler | WebSocketHandler> {\n    const { initialHandlers, handlers } = this.context\n    return handlers.concat(initialHandlers)\n  }\n}\nexport class SetupServerApi\n  extends SetupServerCommonApi\n  implements SetupServer\n{\n  constructor(\n    handlers: Array<RequestHandler | WebSocketHandler>,\n    interceptors: Array<Interceptor<HttpRequestEventMap>> = [\n      new ClientRequestInterceptor(),\n      new XMLHttpRequestInterceptor(),\n      new FetchInterceptor(),\n    ],\n  ) {\n    super(interceptors, handlers)\n\n    this.handlersController = new AsyncHandlersController(handlers)\n  }\n\n  public boundary<Args extends Array<any>, R>(\n    callback: (...args: Args) => R,\n  ): (...args: Args) => R {\n    return (...args: Args): R => {\n      return store.run<any, any>(\n        {\n          initialHandlers: this.handlersController.currentHandlers(),\n          handlers: [],\n        },\n        callback,\n        ...args,\n      )\n    }\n  }\n\n  public close(): void {\n    super.close()\n    store.disable()\n  }\n}\n", "/**\n * @note This API is extended by both \"msw/node\" and \"msw/native\"\n * so be minding about the things you import!\n */\nimport type { RequiredDeep } from 'type-fest'\nimport { invariant } from 'outvariant'\nimport {\n  BatchInterceptor,\n  InterceptorReadyState,\n  type HttpRequestEventMap,\n  type Interceptor,\n} from '@mswjs/interceptors'\nimport type { LifeCycleEventsMap, SharedOptions } from '~/core/sharedOptions'\nimport { SetupApi } from '~/core/SetupApi'\nimport { handleRequest } from '~/core/utils/handleRequest'\nimport type { RequestHandler } from '~/core/handlers/RequestHandler'\nimport type { WebSocketHandler } from '~/core/handlers/WebSocketHandler'\nimport { mergeRight } from '~/core/utils/internal/mergeRight'\nimport { InternalError, devUtils } from '~/core/utils/internal/devUtils'\nimport type { SetupServerCommon } from './glossary'\nimport { handleWebSocketEvent } from '~/core/ws/handleWebSocketEvent'\nimport { webSocketInterceptor } from '~/core/ws/webSocketInterceptor'\nimport { isHandlerKind } from '~/core/utils/internal/isHandlerKind'\n\nconst DEFAULT_LISTEN_OPTIONS: RequiredDeep<SharedOptions> = {\n  onUnhandledRequest: 'warn',\n}\n\nexport class SetupServerCommonApi\n  extends SetupApi<LifeCycleEventsMap>\n  implements SetupServerCommon\n{\n  protected readonly interceptor: BatchInterceptor<\n    Array<Interceptor<HttpRequestEventMap>>,\n    HttpRequestEventMap\n  >\n  private resolvedOptions: RequiredDeep<SharedOptions>\n\n  constructor(\n    interceptors: Array<Interceptor<HttpRequestEventMap>>,\n    handlers: Array<RequestHandler | WebSocketHandler>,\n  ) {\n    super(...handlers)\n\n    this.interceptor = new BatchInterceptor({\n      name: 'setup-server',\n      interceptors,\n    })\n\n    this.resolvedOptions = {} as RequiredDeep<SharedOptions>\n  }\n\n  /**\n   * Subscribe to all requests that are using the interceptor object\n   */\n  private init(): void {\n    this.interceptor.on(\n      'request',\n      async ({ request, requestId, controller }) => {\n        const response = await handleRequest(\n          request,\n          requestId,\n          this.handlersController\n            .currentHandlers()\n            .filter(isHandlerKind('RequestHandler')),\n          this.resolvedOptions,\n          this.emitter,\n          {\n            onPassthroughResponse(request) {\n              const acceptHeader = request.headers.get('accept')\n\n              /**\n               * @note Remove the internal bypass request header.\n               * In the browser, this is done by the worker script.\n               * In Node.js, it has to be done here.\n               */\n              if (acceptHeader) {\n                const nextAcceptHeader = acceptHeader.replace(\n                  /(,\\s+)?msw\\/passthrough/,\n                  '',\n                )\n\n                if (nextAcceptHeader) {\n                  request.headers.set('accept', nextAcceptHeader)\n                } else {\n                  request.headers.delete('accept')\n                }\n              }\n            },\n          },\n        )\n\n        if (response) {\n          controller.respondWith(response)\n        }\n\n        return\n      },\n    )\n\n    this.interceptor.on('unhandledException', ({ error }) => {\n      if (error instanceof InternalError) {\n        throw error\n      }\n    })\n\n    this.interceptor.on(\n      'response',\n      ({ response, isMockedResponse, request, requestId }) => {\n        this.emitter.emit(\n          isMockedResponse ? 'response:mocked' : 'response:bypass',\n          {\n            response,\n            request,\n            requestId,\n          },\n        )\n      },\n    )\n\n    // Preconfigure the WebSocket interception but don't enable it just yet.\n    // It will be enabled when the server starts.\n    handleWebSocketEvent({\n      getUnhandledRequestStrategy: () => {\n        return this.resolvedOptions.onUnhandledRequest\n      },\n      getHandlers: () => {\n        return this.handlersController.currentHandlers()\n      },\n      onMockedConnection: () => {},\n      onPassthroughConnection: () => {},\n    })\n  }\n\n  public listen(options: Partial<SharedOptions> = {}): void {\n    this.resolvedOptions = mergeRight(\n      DEFAULT_LISTEN_OPTIONS,\n      options,\n    ) as RequiredDeep<SharedOptions>\n\n    // Apply the interceptor when starting the server.\n    // Attach the event listeners to the interceptor here\n    // so they get re-attached whenever `.listen()` is called.\n    this.interceptor.apply()\n    this.init()\n    this.subscriptions.push(() => this.interceptor.dispose())\n\n    // Apply the WebSocket interception.\n    webSocketInterceptor.apply()\n    this.subscriptions.push(() => webSocketInterceptor.dispose())\n\n    // Assert that the interceptor has been applied successfully.\n    // Also guards us from forgetting to call \"interceptor.apply()\"\n    // as a part of the \"listen\" method.\n    invariant(\n      [InterceptorReadyState.APPLYING, InterceptorReadyState.APPLIED].includes(\n        this.interceptor.readyState,\n      ),\n      devUtils.formatMessage(\n        'Failed to start \"setupServer\": the interceptor failed to apply. This is likely an issue with the library and you should report it at \"%s\".',\n      ),\n      'https://github.com/mswjs/msw/issues/new/choose',\n    )\n  }\n\n  public close(): void {\n    this.dispose()\n  }\n}\n", "import type { RequestHand<PERSON> } from '~/core/handlers/RequestHandler'\nimport type { WebSocketHandler } from '~/core/handlers/WebSocketHandler'\nimport { SetupServerApi } from './SetupServerApi'\n\n/**\n * Sets up a requests interception in Node.js with the given request handlers.\n * @param {RequestHandler[]} handlers List of request handlers.\n *\n * @see {@link https://mswjs.io/docs/api/setup-server `setupServer()` API reference}\n */\nexport const setupServer = (\n  ...handlers: Array<RequestHandler | WebSocketHandler>\n): SetupServerApi => {\n  return new SetupServerApi(handlers)\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAElC,SAAS,gCAAgC;AACzC,SAAS,iCAAiC;AAC1C,SAAS,wBAAwB;;;ACCjC,SAAS,iBAAiB;AAC1B;AAAA,EACE;AAAA,EACA;AAAA,OAGK;AAEP,SAAS,gBAAgB;AACzB,SAAS,qBAAqB;AAG9B,SAAS,kBAAkB;AAC3B,SAAS,eAAe,gBAAgB;AAExC,SAAS,4BAA4B;AACrC,SAAS,4BAA4B;AACrC,SAAS,qBAAqB;AAE9B,IAAM,yBAAsD;AAAA,EAC1D,oBAAoB;AACtB;AAEO,IAAM,uBAAN,cACG,SAEV;AAAA,EACqB;AAAA,EAIX;AAAA,EAER,YACE,cACA,UACA;AACA,UAAM,GAAG,QAAQ;AAEjB,SAAK,cAAc,IAAI,iBAAiB;AAAA,MACtC,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAED,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKQ,OAAa;AACnB,SAAK,YAAY;AAAA,MACf;AAAA,MACA,OAAO,EAAE,SAAS,WAAW,WAAW,MAAM;AAC5C,cAAM,WAAW,MAAM;AAAA,UACrB;AAAA,UACA;AAAA,UACA,KAAK,mBACF,gBAAgB,EAChB,OAAO,cAAc,gBAAgB,CAAC;AAAA,UACzC,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,sBAAsBA,UAAS;AAC7B,oBAAM,eAAeA,SAAQ,QAAQ,IAAI,QAAQ;AAOjD,kBAAI,cAAc;AAChB,sBAAM,mBAAmB,aAAa;AAAA,kBACpC;AAAA,kBACA;AAAA,gBACF;AAEA,oBAAI,kBAAkB;AACpB,kBAAAA,SAAQ,QAAQ,IAAI,UAAU,gBAAgB;AAAA,gBAChD,OAAO;AACL,kBAAAA,SAAQ,QAAQ,OAAO,QAAQ;AAAA,gBACjC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,UAAU;AACZ,qBAAW,YAAY,QAAQ;AAAA,QACjC;AAEA;AAAA,MACF;AAAA,IACF;AAEA,SAAK,YAAY,GAAG,sBAAsB,CAAC,EAAE,MAAM,MAAM;AACvD,UAAI,iBAAiB,eAAe;AAClC,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAED,SAAK,YAAY;AAAA,MACf;AAAA,MACA,CAAC,EAAE,UAAU,kBAAkB,SAAS,UAAU,MAAM;AACtD,aAAK,QAAQ;AAAA,UACX,mBAAmB,oBAAoB;AAAA,UACvC;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAIA,yBAAqB;AAAA,MACnB,6BAA6B,MAAM;AACjC,eAAO,KAAK,gBAAgB;AAAA,MAC9B;AAAA,MACA,aAAa,MAAM;AACjB,eAAO,KAAK,mBAAmB,gBAAgB;AAAA,MACjD;AAAA,MACA,oBAAoB,MAAM;AAAA,MAAC;AAAA,MAC3B,yBAAyB,MAAM;AAAA,MAAC;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EAEO,OAAO,UAAkC,CAAC,GAAS;AACxD,SAAK,kBAAkB;AAAA,MACrB;AAAA,MACA;AAAA,IACF;AAKA,SAAK,YAAY,MAAM;AACvB,SAAK,KAAK;AACV,SAAK,cAAc,KAAK,MAAM,KAAK,YAAY,QAAQ,CAAC;AAGxD,yBAAqB,MAAM;AAC3B,SAAK,cAAc,KAAK,MAAM,qBAAqB,QAAQ,CAAC;AAK5D;AAAA,MACE,CAAC,sBAAsB,UAAU,sBAAsB,OAAO,EAAE;AAAA,QAC9D,KAAK,YAAY;AAAA,MACnB;AAAA,MACA,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEO,QAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AACF;;;AD7JA,IAAM,QAAQ,IAAI,kBAA0C;AAY5D,IAAM,0BAAN,MAA4D;AAAA,EAClD;AAAA,EAER,YAAY,iBAA2D;AACrE,SAAK,cAAc,EAAE,iBAAiB,UAAU,CAAC,EAAE;AAAA,EACrD;AAAA,EAEA,IAAI,UAAkC;AACpC,WAAO,MAAM,SAAS,KAAK,KAAK;AAAA,EAClC;AAAA,EAEO,QAAQ,iBAA2D;AACxE,SAAK,QAAQ,SAAS,QAAQ,GAAG,eAAe;AAAA,EAClD;AAAA,EAEO,MAAM,cAAwD;AACnE,UAAM,UAAU,KAAK;AACrB,YAAQ,WAAW,CAAC;AACpB,YAAQ,kBACN,aAAa,SAAS,IAAI,eAAe,QAAQ;AAAA,EACrD;AAAA,EAEO,kBAA4D;AACjE,UAAM,EAAE,iBAAiB,SAAS,IAAI,KAAK;AAC3C,WAAO,SAAS,OAAO,eAAe;AAAA,EACxC;AACF;AACO,IAAM,iBAAN,cACG,qBAEV;AAAA,EACE,YACE,UACA,eAAwD;AAAA,IACtD,IAAI,yBAAyB;AAAA,IAC7B,IAAI,0BAA0B;AAAA,IAC9B,IAAI,iBAAiB;AAAA,EACvB,GACA;AACA,UAAM,cAAc,QAAQ;AAE5B,SAAK,qBAAqB,IAAI,wBAAwB,QAAQ;AAAA,EAChE;AAAA,EAEO,SACL,UACsB;AACtB,WAAO,IAAI,SAAkB;AAC3B,aAAO,MAAM;AAAA,QACX;AAAA,UACE,iBAAiB,KAAK,mBAAmB,gBAAgB;AAAA,UACzD,UAAU,CAAC;AAAA,QACb;AAAA,QACA;AAAA,QACA,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EAEO,QAAc;AACnB,UAAM,MAAM;AACZ,UAAM,QAAQ;AAAA,EAChB;AACF;;;AE5EO,IAAM,cAAc,IACtB,aACgB;AACnB,SAAO,IAAI,eAAe,QAAQ;AACpC;", "names": ["request"]}