{"version": 3, "sources": ["../../../../src/core/utils/HttpResponse/decorators.ts"], "sourcesContent": ["import statuses from '@bundled-es-modules/statuses'\nimport { Headers as HeadersPolyfill } from 'headers-polyfill'\nimport type { HttpResponseInit } from '../../HttpResponse'\n\nconst { message } = statuses\n\nexport const kSetCookie = Symbol('kSetCookie')\n\nexport interface HttpResponseDecoratedInit extends HttpResponseInit {\n  status: number\n  statusText: string\n  headers: Headers\n}\n\nexport function normalizeResponseInit(\n  init: HttpResponseInit = {},\n): HttpResponseDecoratedInit {\n  const status = init?.status || 200\n  const statusText = init?.statusText || message[status] || ''\n  const headers = new Headers(init?.headers)\n\n  return {\n    ...init,\n    headers,\n    status,\n    statusText,\n  }\n}\n\nexport function decorateResponse(\n  response: Response,\n  init: HttpResponseDecoratedInit,\n): Response {\n  // Allow to mock the response type.\n  if (init.type) {\n    Object.defineProperty(response, 'type', {\n      value: init.type,\n      enumerable: true,\n      writable: false,\n    })\n  }\n\n  const responseCookies = init.headers.get('set-cookie')\n\n  if (responseCookies) {\n    // Record the raw \"Set-Cookie\" response header provided\n    // in the HeadersInit. This is later used to store these cookies\n    // in cookie jar and return the right cookies in the \"cookies\"\n    // response resolver argument.\n    Object.defineProperty(response, kSetCookie, {\n      value: responseCookies,\n      enumerable: false,\n      writable: false,\n    })\n\n    // Cookie forwarding is only relevant in the browser.\n    if (typeof document !== 'undefined') {\n      // Write the mocked response cookies to the document.\n      // Use `headers-polyfill` to get the Set-Cookie header value correctly.\n      // This is an alternative until TypeScript 5.2\n      // and Node.js v20 become the minimum supported version\n      // and getSetCookie in Headers can be used directly.\n      const responseCookiePairs = HeadersPolyfill.prototype.getSetCookie.call(\n        init.headers,\n      )\n\n      for (const cookieString of responseCookiePairs) {\n        // No need to parse the cookie headers because it's defined\n        // as the valid cookie string to begin with.\n        document.cookie = cookieString\n      }\n    }\n  }\n\n  return response\n}\n"], "mappings": "AAAA,OAAO,cAAc;AACrB,SAAS,WAAW,uBAAuB;AAG3C,MAAM,EAAE,QAAQ,IAAI;AAEb,MAAM,aAAa,OAAO,YAAY;AAQtC,SAAS,sBACd,OAAyB,CAAC,GACC;AAC3B,QAAM,SAAS,MAAM,UAAU;AAC/B,QAAM,aAAa,MAAM,cAAc,QAAQ,MAAM,KAAK;AAC1D,QAAM,UAAU,IAAI,QAAQ,MAAM,OAAO;AAEzC,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,iBACd,UACA,MACU;AAEV,MAAI,KAAK,MAAM;AACb,WAAO,eAAe,UAAU,QAAQ;AAAA,MACtC,OAAO,KAAK;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,QAAM,kBAAkB,KAAK,QAAQ,IAAI,YAAY;AAErD,MAAI,iBAAiB;AAKnB,WAAO,eAAe,UAAU,YAAY;AAAA,MAC1C,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAGD,QAAI,OAAO,aAAa,aAAa;AAMnC,YAAM,sBAAsB,gBAAgB,UAAU,aAAa;AAAA,QACjE,KAAK;AAAA,MACP;AAEA,iBAAW,gBAAgB,qBAAqB;AAG9C,iBAAS,SAAS;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;", "names": []}