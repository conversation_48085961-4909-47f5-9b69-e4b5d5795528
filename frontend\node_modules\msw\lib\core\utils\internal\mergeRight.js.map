{"version": 3, "sources": ["../../../../src/core/utils/internal/mergeRight.ts"], "sourcesContent": ["import { isObject } from './isObject'\n\n/**\n * Deeply merges two given objects with the right one\n * having a priority during property assignment.\n */\nexport function mergeRight(\n  left: Record<string, any>,\n  right: Record<string, any>,\n) {\n  return Object.entries(right).reduce(\n    (result, [key, rightValue]) => {\n      const leftValue = result[key]\n\n      if (Array.isArray(leftValue) && Array.isArray(rightValue)) {\n        result[key] = leftValue.concat(rightValue)\n        return result\n      }\n\n      if (isObject(leftValue) && isObject(rightValue)) {\n        result[key] = mergeRight(leftValue, rightValue)\n        return result\n      }\n\n      result[key] = rightValue\n      return result\n    },\n    Object.assign({}, left),\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAyB;AAMlB,SAAS,WACd,MACA,OACA;AACA,SAAO,OAAO,QAAQ,KAAK,EAAE;AAAA,IAC3B,CAAC,QAAQ,CAAC,KAAK,UAAU,MAAM;AAC7B,YAAM,YAAY,OAAO,GAAG;AAE5B,UAAI,MAAM,QAAQ,SAAS,KAAK,MAAM,QAAQ,UAAU,GAAG;AACzD,eAAO,GAAG,IAAI,UAAU,OAAO,UAAU;AACzC,eAAO;AAAA,MACT;AAEA,cAAI,0BAAS,SAAS,SAAK,0BAAS,UAAU,GAAG;AAC/C,eAAO,GAAG,IAAI,WAAW,WAAW,UAAU;AAC9C,eAAO;AAAA,MACT;AAEA,aAAO,GAAG,IAAI;AACd,aAAO;AAAA,IACT;AAAA,IACA,OAAO,OAAO,CAAC,GAAG,IAAI;AAAA,EACxB;AACF;", "names": []}