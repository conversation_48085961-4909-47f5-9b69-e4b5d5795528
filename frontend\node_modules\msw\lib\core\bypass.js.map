{"version": 3, "sources": ["../../src/core/bypass.ts"], "sourcesContent": ["import { invariant } from 'outvariant'\n\nexport type BypassRequestInput = string | URL | Request\n\n/**\n * Creates a `Request` instance that will always be ignored by MSW.\n *\n * @example\n * import { bypass } from 'msw'\n *\n * fetch(bypass('/resource'))\n * fetch(bypass(new URL('/resource', 'https://example.com)))\n * fetch(bypass(new Request('https://example.com/resource')))\n *\n * @see {@link https://mswjs.io/docs/api/bypass `bypass()` API reference}\n */\nexport function bypass(input: BypassRequestInput, init?: RequestInit): Request {\n  // Always create a new Request instance.\n  // This way, the \"init\" modifications will propagate\n  // to the bypass request instance automatically.\n  const request = new Request(\n    // If given a Request instance, clone it not to exhaust\n    // the original request's body.\n    input instanceof Request ? input.clone() : input,\n    init,\n  )\n\n  invariant(\n    !request.bodyUsed,\n    'Failed to create a bypassed request to \"%s %s\": given request instance already has its body read. Make sure to clone the intercepted request if you wish to read its body before bypassing it.',\n    request.method,\n    request.url,\n  )\n\n  const requestClone = request.clone()\n\n  /**\n   * Send the internal request header that would instruct MSW\n   * to perform this request as-is, ignoring any matching handlers.\n   * @note Use the `accept` header to support scenarios when the\n   * request cannot have headers (e.g. `sendBeacon` requests).\n   */\n  requestClone.headers.append('accept', 'msw/passthrough')\n\n  return requestClone\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAA0B;AAgBnB,SAAS,OAAO,OAA2B,MAA6B;AAI7E,QAAM,UAAU,IAAI;AAAA;AAAA;AAAA,IAGlB,iBAAiB,UAAU,MAAM,MAAM,IAAI;AAAA,IAC3C;AAAA,EACF;AAEA;AAAA,IACE,CAAC,QAAQ;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AAEA,QAAM,eAAe,QAAQ,MAAM;AAQnC,eAAa,QAAQ,OAAO,UAAU,iBAAiB;AAEvD,SAAO;AACT;", "names": []}