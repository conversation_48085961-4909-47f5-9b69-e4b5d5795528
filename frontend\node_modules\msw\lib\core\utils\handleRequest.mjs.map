{"version": 3, "sources": ["../../../src/core/utils/handleRequest.ts"], "sourcesContent": ["import { until } from '@open-draft/until'\nimport { Emitter } from 'strict-event-emitter'\nimport { LifeCycleEventsMap, SharedOptions } from '../sharedOptions'\nimport { RequiredDeep } from '../typeUtils'\nimport type { RequestHandler } from '../handlers/RequestHandler'\nimport { HandlersExecutionResult, executeHandlers } from './executeHandlers'\nimport { onUnhandledRequest } from './request/onUnhandledRequest'\nimport { storeResponseCookies } from './request/storeResponseCookies'\n\nexport interface HandleRequestOptions {\n  /**\n   * `resolutionContext` is not part of the general public api\n   * but is exposed to aid in creating extensions like\n   * `@mswjs/http-middleware`.\n   */\n  resolutionContext?: {\n    /**\n     * A base url to use when resolving relative urls.\n     * @note This is primarily used by the `@mswjs/http-middleware`\n     * to resolve relative urls in the context of the running server\n     */\n    baseUrl?: string\n  }\n\n  /**\n   * Invoked whenever a request is performed as-is.\n   */\n  onPassthroughResponse?(request: Request): void\n\n  /**\n   * Invoked when the mocked response is ready to be sent.\n   */\n  onMockedResponse?(\n    response: Response,\n    handler: RequiredDeep<HandlersExecutionResult>,\n  ): void\n}\n\nexport async function handleRequest(\n  request: Request,\n  requestId: string,\n  handlers: Array<RequestHandler>,\n  options: RequiredDeep<SharedOptions>,\n  emitter: Emitter<LifeCycleEventsMap>,\n  handleRequestOptions?: HandleRequestOptions,\n): Promise<Response | undefined> {\n  emitter.emit('request:start', { request, requestId })\n\n  // Perform requests wrapped in \"bypass()\" as-is.\n  if (request.headers.get('accept')?.includes('msw/passthrough')) {\n    emitter.emit('request:end', { request, requestId })\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // Resolve a mocked response from the list of request handlers.\n  const lookupResult = await until(() => {\n    return executeHandlers({\n      request,\n      requestId,\n      handlers,\n      resolutionContext: handleRequestOptions?.resolutionContext,\n    })\n  })\n\n  if (lookupResult.error) {\n    // Allow developers to react to unhandled exceptions in request handlers.\n    emitter.emit('unhandledException', {\n      error: lookupResult.error,\n      request,\n      requestId,\n    })\n    throw lookupResult.error\n  }\n\n  // If the handler lookup returned nothing, no request handler was found\n  // matching this request. Report the request as unhandled.\n  if (!lookupResult.data) {\n    await onUnhandledRequest(request, options.onUnhandledRequest)\n    emitter.emit('request:unhandled', { request, requestId })\n    emitter.emit('request:end', { request, requestId })\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  const { response } = lookupResult.data\n\n  // When the handled request returned no mocked response, warn the developer,\n  // as it may be an oversight on their part. Perform the request as-is.\n  if (!response) {\n    emitter.emit('request:end', { request, requestId })\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // Perform the request as-is when the developer explicitly returned \"req.passthrough()\".\n  // This produces no warning as the request was handled.\n  if (\n    response.status === 302 &&\n    response.headers.get('x-msw-intention') === 'passthrough'\n  ) {\n    emitter.emit('request:end', { request, requestId })\n    handleRequestOptions?.onPassthroughResponse?.(request)\n    return\n  }\n\n  // Store all the received response cookies in the cookie jar.\n  storeResponseCookies(request, response)\n\n  emitter.emit('request:match', { request, requestId })\n\n  const requiredLookupResult =\n    lookupResult.data as RequiredDeep<HandlersExecutionResult>\n\n  handleRequestOptions?.onMockedResponse?.(response, requiredLookupResult)\n\n  emitter.emit('request:end', { request, requestId })\n\n  return response\n}\n"], "mappings": "AAAA,SAAS,aAAa;AAKtB,SAAkC,uBAAuB;AACzD,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AA+BrC,eAAsB,cACpB,SACA,WACA,UACA,SACA,SACA,sBAC+B;AAC/B,UAAQ,KAAK,iBAAiB,EAAE,SAAS,UAAU,CAAC;AAGpD,MAAI,QAAQ,QAAQ,IAAI,QAAQ,GAAG,SAAS,iBAAiB,GAAG;AAC9D,YAAQ,KAAK,eAAe,EAAE,SAAS,UAAU,CAAC;AAClD,0BAAsB,wBAAwB,OAAO;AACrD;AAAA,EACF;AAGA,QAAM,eAAe,MAAM,MAAM,MAAM;AACrC,WAAO,gBAAgB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA,mBAAmB,sBAAsB;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC;AAED,MAAI,aAAa,OAAO;AAEtB,YAAQ,KAAK,sBAAsB;AAAA,MACjC,OAAO,aAAa;AAAA,MACpB;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,aAAa;AAAA,EACrB;AAIA,MAAI,CAAC,aAAa,MAAM;AACtB,UAAM,mBAAmB,SAAS,QAAQ,kBAAkB;AAC5D,YAAQ,KAAK,qBAAqB,EAAE,SAAS,UAAU,CAAC;AACxD,YAAQ,KAAK,eAAe,EAAE,SAAS,UAAU,CAAC;AAClD,0BAAsB,wBAAwB,OAAO;AACrD;AAAA,EACF;AAEA,QAAM,EAAE,SAAS,IAAI,aAAa;AAIlC,MAAI,CAAC,UAAU;AACb,YAAQ,KAAK,eAAe,EAAE,SAAS,UAAU,CAAC;AAClD,0BAAsB,wBAAwB,OAAO;AACrD;AAAA,EACF;AAIA,MACE,SAAS,WAAW,OACpB,SAAS,QAAQ,IAAI,iBAAiB,MAAM,eAC5C;AACA,YAAQ,KAAK,eAAe,EAAE,SAAS,UAAU,CAAC;AAClD,0BAAsB,wBAAwB,OAAO;AACrD;AAAA,EACF;AAGA,uBAAqB,SAAS,QAAQ;AAEtC,UAAQ,KAAK,iBAAiB,EAAE,SAAS,UAAU,CAAC;AAEpD,QAAM,uBACJ,aAAa;AAEf,wBAAsB,mBAAmB,UAAU,oBAAoB;AAEvE,UAAQ,KAAK,eAAe,EAAE,SAAS,UAAU,CAAC;AAElD,SAAO;AACT;", "names": []}