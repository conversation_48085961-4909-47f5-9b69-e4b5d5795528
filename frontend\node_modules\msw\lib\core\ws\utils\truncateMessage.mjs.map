{"version": 3, "sources": ["../../../../src/core/ws/utils/truncateMessage.ts"], "sourcesContent": ["const MAX_LENGTH = 24\n\nexport function truncateMessage(message: string): string {\n  if (message.length <= MAX_LENGTH) {\n    return message\n  }\n\n  return `${message.slice(0, MAX_LENGTH)}…`\n}\n"], "mappings": "AAAA,MAAM,aAAa;AAEZ,SAAS,gBAAgB,SAAyB;AACvD,MAAI,QAAQ,UAAU,YAAY;AAChC,WAAO;AAAA,EACT;AAEA,SAAO,GAAG,QAAQ,MAAM,GAAG,UAAU,CAAC;AACxC;", "names": []}