declare const _default: ((path: string, opts?: import("./opts-arg.js").MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
    mkdirpSync: (path: string, opts?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void;
    mkdirpNative: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
        sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
    };
    mkdirpNativeSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
    mkdirpManual: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => Promise<string | void | undefined>) & {
        sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
    };
    mkdirpManualSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
    sync: (path: string, opts?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void;
    native: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
        sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
    };
    nativeSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
    manual: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => Promise<string | void | undefined>) & {
        sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
    };
    manualSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
    useNative: ((opts?: import("./opts-arg.js").MkdirpOptions | undefined) => boolean) & {
        sync: (opts?: import("./opts-arg.js").MkdirpOptions | undefined) => boolean;
    };
    useNativeSync: (opts?: import("./opts-arg.js").MkdirpOptions | undefined) => boolean;
} & {
    default: ((path: string, opts?: import("./opts-arg.js").MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
        mkdirpSync: (path: string, opts?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void;
        mkdirpNative: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
            sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
        };
        mkdirpNativeSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
        mkdirpManual: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => Promise<string | void | undefined>) & {
            sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
        };
        mkdirpManualSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
        sync: (path: string, opts?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void;
        native: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
            sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
        };
        nativeSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
        manual: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => Promise<string | void | undefined>) & {
            sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
        };
        manualSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
        useNative: ((opts?: import("./opts-arg.js").MkdirpOptions | undefined) => boolean) & {
            sync: (opts?: import("./opts-arg.js").MkdirpOptions | undefined) => boolean;
        };
        useNativeSync: (opts?: import("./opts-arg.js").MkdirpOptions | undefined) => boolean;
    };
    mkdirp: ((path: string, opts?: import("./opts-arg.js").MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
        mkdirpSync: (path: string, opts?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void;
        mkdirpNative: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
            sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
        };
        mkdirpNativeSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
        mkdirpManual: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => Promise<string | void | undefined>) & {
            sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
        };
        mkdirpManualSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
        sync: (path: string, opts?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void;
        native: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => Promise<string | void | undefined>) & {
            sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
        };
        nativeSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined) => string | void | undefined;
        manual: ((path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => Promise<string | void | undefined>) & {
            sync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
        };
        manualSync: (path: string, options?: import("./opts-arg.js").MkdirpOptions | undefined, made?: string | void | undefined) => string | void | undefined;
        useNative: ((opts?: import("./opts-arg.js").MkdirpOptions | undefined) => boolean) & {
            sync: (opts?: import("./opts-arg.js").MkdirpOptions | undefined) => boolean;
        };
        useNativeSync: (opts?: import("./opts-arg.js").MkdirpOptions | undefined) => boolean;
    };
};
export = _default;
//# sourceMappingURL=index-cjs.d.ts.map