{"version": 3, "sources": ["../../src/core/delay.ts"], "sourcesContent": ["import { isNodeProcess } from 'is-node-process'\n\nexport const SET_TIMEOUT_MAX_ALLOWED_INT = 2147483647\nexport const MIN_SERVER_RESPONSE_TIME = 100\nexport const MAX_SERVER_RESPONSE_TIME = 400\nexport const NODE_SERVER_RESPONSE_TIME = 5\n\nfunction getRealisticResponseTime(): number {\n  if (isNodeProcess()) {\n    return NODE_SERVER_RESPONSE_TIME\n  }\n\n  return Math.floor(\n    Math.random() * (MAX_SERVER_RESPONSE_TIME - MIN_SERVER_RESPONSE_TIME) +\n      MIN_SERVER_RESPONSE_TIME,\n  )\n}\n\nexport type DelayMode = 'real' | 'infinite'\n\n/**\n * Delays the response by the given duration (ms).\n *\n * @example\n * await delay() // emulate realistic server response time\n * await delay(1200) // delay response by 1200ms\n * await delay('infinite') // delay response infinitely\n *\n * @see {@link https://mswjs.io/docs/api/delay `delay()` API reference}\n */\nexport async function delay(\n  durationOrMode?: DelayMode | number,\n): Promise<void> {\n  let delayTime: number\n\n  if (typeof durationOrMode === 'string') {\n    switch (durationOrMode) {\n      case 'infinite': {\n        // Using `Infinity` as a delay value executes the response timeout immediately.\n        // Instead, use the maximum allowed integer for `setTimeout`.\n        delayTime = SET_TIMEOUT_MAX_ALLOWED_INT\n        break\n      }\n      case 'real': {\n        delayTime = getRealisticResponseTime()\n        break\n      }\n      default: {\n        throw new Error(\n          `Failed to delay a response: unknown delay mode \"${durationOrMode}\". Please make sure you provide one of the supported modes (\"real\", \"infinite\") or a number.`,\n        )\n      }\n    }\n  } else if (typeof durationOrMode === 'undefined') {\n    // Use random realistic server response time when no explicit delay duration was provided.\n    delayTime = getRealisticResponseTime()\n  } else {\n    // Guard against passing values like `Infinity` or `Number.MAX_VALUE`\n    // as the response delay duration. They don't produce the result you may expect.\n    if (durationOrMode > SET_TIMEOUT_MAX_ALLOWED_INT) {\n      throw new Error(\n        `Failed to delay a response: provided delay duration (${durationOrMode}) exceeds the maximum allowed duration for \"setTimeout\" (${SET_TIMEOUT_MAX_ALLOWED_INT}). This will cause the response to be returned immediately. Please use a number within the allowed range to delay the response by exact duration, or consider the \"infinite\" delay mode to delay the response indefinitely.`,\n      )\n    }\n\n    delayTime = durationOrMode\n  }\n\n  return new Promise((resolve) => setTimeout(resolve, delayTime))\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAA8B;AAEvB,MAAM,8BAA8B;AACpC,MAAM,2BAA2B;AACjC,MAAM,2BAA2B;AACjC,MAAM,4BAA4B;AAEzC,SAAS,2BAAmC;AAC1C,UAAI,sCAAc,GAAG;AACnB,WAAO;AAAA,EACT;AAEA,SAAO,KAAK;AAAA,IACV,KAAK,OAAO,KAAK,2BAA2B,4BAC1C;AAAA,EACJ;AACF;AAcA,eAAsB,MACpB,gBACe;AACf,MAAI;AAEJ,MAAI,OAAO,mBAAmB,UAAU;AACtC,YAAQ,gBAAgB;AAAA,MACtB,KAAK,YAAY;AAGf,oBAAY;AACZ;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,oBAAY,yBAAyB;AACrC;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,IAAI;AAAA,UACR,mDAAmD,cAAc;AAAA,QACnE;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,OAAO,mBAAmB,aAAa;AAEhD,gBAAY,yBAAyB;AAAA,EACvC,OAAO;AAGL,QAAI,iBAAiB,6BAA6B;AAChD,YAAM,IAAI;AAAA,QACR,wDAAwD,cAAc,4DAA4D,2BAA2B;AAAA,MAC/J;AAAA,IACF;AAEA,gBAAY;AAAA,EACd;AAEA,SAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,SAAS,CAAC;AAChE;", "names": []}