{"version": 3, "sources": ["../../../../src/core/utils/internal/jsonParse.ts"], "sourcesContent": ["/**\n * Parses a given value into a JSON.\n * Does not throw an exception on an invalid JSON string.\n */\nexport function jsonParse<ValueType extends Record<string, any>>(\n  value: any,\n): ValueType | undefined {\n  try {\n    return JSON.parse(value)\n  } catch {\n    return undefined\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIO,SAAS,UACd,OACuB;AACvB,MAAI;AACF,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB,QAAQ;AACN,WAAO;AAAA,EACT;AACF;", "names": []}