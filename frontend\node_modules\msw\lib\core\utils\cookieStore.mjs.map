{"version": 3, "sources": ["../../../src/core/utils/cookieStore.ts"], "sourcesContent": ["import { invariant } from 'outvariant'\nimport { isNodeProcess } from 'is-node-process'\nimport tough<PERSON><PERSON><PERSON>, {\n  type <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,\n} from '@bundled-es-modules/tough-cookie'\n\nconst { <PERSON><PERSON>, CookieJar, Store, MemoryCookieStore, domainMatch, pathMatch } =\n  toughCookie\n\n/**\n * Custom cookie store that uses the Web Storage API.\n * @see https://github.com/expo/tough-cookie-web-storage-store\n */\nclass WebStorageCookieStore extends Store {\n  private storage: Storage\n  private storageKey: string\n\n  constructor() {\n    super()\n\n    invariant(\n      typeof localStorage !== 'undefined',\n      'Failed to create a WebStorageCookieStore: `localStorage` is not available in this environment. This is likely an issue with MSW. Please report it on GitHub: https://github.com/mswjs/msw/issues',\n    )\n\n    this.synchronous = true\n    this.storage = localStorage\n    this.storageKey = '__msw-cookie-store__'\n  }\n\n  findCookie(\n    domain: string,\n    path: string,\n    key: string,\n    callback: (error: Error | null, cookie: CookieInstance | null) => void,\n  ): void {\n    try {\n      const store = this.getStore()\n      const cookies = this.filterCookiesFromList(store, { domain, path, key })\n      callback(null, cookies[0] || null)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error, null)\n      }\n    }\n  }\n\n  findCookies(\n    domain: string,\n    path: string,\n    allowSpecialUseDomain: boolean,\n    callback: (error: Error | null, cookie: Array<CookieInstance>) => void,\n  ): void {\n    if (!domain) {\n      callback(null, [])\n      return\n    }\n\n    try {\n      const store = this.getStore()\n      const results = this.filterCookiesFromList(store, {\n        domain,\n        path,\n      })\n      callback(null, results)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error, [])\n      }\n    }\n  }\n\n  putCookie(\n    cookie: CookieInstance,\n    callback: (error: Error | null) => void,\n  ): void {\n    try {\n      // Never set cookies with `maxAge` of `0`.\n      if (cookie.maxAge === 0) {\n        return\n      }\n\n      const store = this.getStore()\n      store.push(cookie)\n      this.updateStore(store)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error)\n      }\n    }\n  }\n\n  updateCookie(\n    oldCookie: CookieInstance,\n    newCookie: CookieInstance,\n    callback: (error: Error | null) => void,\n  ): void {\n    /**\n     * If updating a cookie with `maxAge` of `0`, remove it from the store.\n     * Otherwise, two cookie entries will be created.\n     * @see https://github.com/mswjs/msw/issues/2272\n     */\n    if (newCookie.maxAge === 0) {\n      this.removeCookie(\n        newCookie.domain || '',\n        newCookie.path || '',\n        newCookie.key,\n        callback,\n      )\n      return\n    }\n\n    this.putCookie(newCookie, callback)\n  }\n\n  removeCookie(\n    domain: string,\n    path: string,\n    key: string,\n    callback: (error: Error | null) => void,\n  ): void {\n    try {\n      const store = this.getStore()\n      const nextStore = this.deleteCookiesFromList(store, { domain, path, key })\n      this.updateStore(nextStore)\n      callback(null)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error)\n      }\n    }\n  }\n\n  removeCookies(\n    domain: string,\n    path: string,\n    callback: (error: Error | null) => void,\n  ): void {\n    try {\n      const store = this.getStore()\n      const nextStore = this.deleteCookiesFromList(store, { domain, path })\n      this.updateStore(nextStore)\n      callback(null)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error)\n      }\n    }\n  }\n\n  getAllCookies(\n    callback: (error: Error | null, cookie: Array<CookieInstance>) => void,\n  ): void {\n    try {\n      callback(null, this.getStore())\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error, [])\n      }\n    }\n  }\n\n  private getStore(): Array<CookieInstance> {\n    try {\n      const json = this.storage.getItem(this.storageKey)\n\n      if (json == null) {\n        return []\n      }\n\n      const rawCookies = JSON.parse(json) as Array<Record<string, any>>\n      const cookies: Array<CookieInstance> = []\n      for (const rawCookie of rawCookies) {\n        const cookie = Cookie.fromJSON(rawCookie)\n        if (cookie != null) {\n          cookies.push(cookie)\n        }\n      }\n      return cookies\n    } catch {\n      return []\n    }\n  }\n\n  private updateStore(nextStore: Array<CookieInstance>) {\n    this.storage.setItem(\n      this.storageKey,\n      JSON.stringify(nextStore.map((cookie) => cookie.toJSON())),\n    )\n  }\n\n  private filterCookiesFromList(\n    cookies: Array<CookieInstance>,\n    matches: { domain?: string; path?: string; key?: string },\n  ): Array<CookieInstance> {\n    const result: Array<CookieInstance> = []\n\n    for (const cookie of cookies) {\n      if (matches.domain && !domainMatch(matches.domain, cookie.domain || '')) {\n        continue\n      }\n\n      if (matches.path && !pathMatch(matches.path, cookie.path || '')) {\n        continue\n      }\n\n      if (matches.key && cookie.key !== matches.key) {\n        continue\n      }\n\n      result.push(cookie)\n    }\n\n    return result\n  }\n\n  private deleteCookiesFromList(\n    cookies: Array<CookieInstance>,\n    matches: { domain?: string; path?: string; key?: string },\n  ) {\n    const matchingCookies = this.filterCookiesFromList(cookies, matches)\n    return cookies.filter((cookie) => !matchingCookies.includes(cookie))\n  }\n}\n\nconst store = isNodeProcess()\n  ? new MemoryCookieStore()\n  : new WebStorageCookieStore()\n\nexport const cookieStore = new CookieJar(store)\n"], "mappings": "AAAA,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;AAC9B,OAAO,iBAEA;AAEP,MAAM,EAAE,QAAQ,WAAW,OAAO,mBAAmB,aAAa,UAAU,IAC1E;AAMF,MAAM,8BAA8B,MAAM;AAAA,EAChC;AAAA,EACA;AAAA,EAER,cAAc;AACZ,UAAM;AAEN;AAAA,MACE,OAAO,iBAAiB;AAAA,MACxB;AAAA,IACF;AAEA,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,WACE,QACA,MACA,KACA,UACM;AACN,QAAI;AACF,YAAMA,SAAQ,KAAK,SAAS;AAC5B,YAAM,UAAU,KAAK,sBAAsBA,QAAO,EAAE,QAAQ,MAAM,IAAI,CAAC;AACvE,eAAS,MAAM,QAAQ,CAAC,KAAK,IAAI;AAAA,IACnC,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,iBAAS,OAAO,IAAI;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YACE,QACA,MACA,uBACA,UACM;AACN,QAAI,CAAC,QAAQ;AACX,eAAS,MAAM,CAAC,CAAC;AACjB;AAAA,IACF;AAEA,QAAI;AACF,YAAMA,SAAQ,KAAK,SAAS;AAC5B,YAAM,UAAU,KAAK,sBAAsBA,QAAO;AAAA,QAChD;AAAA,QACA;AAAA,MACF,CAAC;AACD,eAAS,MAAM,OAAO;AAAA,IACxB,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,iBAAS,OAAO,CAAC,CAAC;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UACE,QACA,UACM;AACN,QAAI;AAEF,UAAI,OAAO,WAAW,GAAG;AACvB;AAAA,MACF;AAEA,YAAMA,SAAQ,KAAK,SAAS;AAC5B,MAAAA,OAAM,KAAK,MAAM;AACjB,WAAK,YAAYA,MAAK;AAAA,IACxB,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,iBAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,aACE,WACA,WACA,UACM;AAMN,QAAI,UAAU,WAAW,GAAG;AAC1B,WAAK;AAAA,QACH,UAAU,UAAU;AAAA,QACpB,UAAU,QAAQ;AAAA,QAClB,UAAU;AAAA,QACV;AAAA,MACF;AACA;AAAA,IACF;AAEA,SAAK,UAAU,WAAW,QAAQ;AAAA,EACpC;AAAA,EAEA,aACE,QACA,MACA,KACA,UACM;AACN,QAAI;AACF,YAAMA,SAAQ,KAAK,SAAS;AAC5B,YAAM,YAAY,KAAK,sBAAsBA,QAAO,EAAE,QAAQ,MAAM,IAAI,CAAC;AACzE,WAAK,YAAY,SAAS;AAC1B,eAAS,IAAI;AAAA,IACf,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,iBAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cACE,QACA,MACA,UACM;AACN,QAAI;AACF,YAAMA,SAAQ,KAAK,SAAS;AAC5B,YAAM,YAAY,KAAK,sBAAsBA,QAAO,EAAE,QAAQ,KAAK,CAAC;AACpE,WAAK,YAAY,SAAS;AAC1B,eAAS,IAAI;AAAA,IACf,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,iBAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cACE,UACM;AACN,QAAI;AACF,eAAS,MAAM,KAAK,SAAS,CAAC;AAAA,IAChC,SAAS,OAAO;AACd,UAAI,iBAAiB,OAAO;AAC1B,iBAAS,OAAO,CAAC,CAAC;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,WAAkC;AACxC,QAAI;AACF,YAAM,OAAO,KAAK,QAAQ,QAAQ,KAAK,UAAU;AAEjD,UAAI,QAAQ,MAAM;AAChB,eAAO,CAAC;AAAA,MACV;AAEA,YAAM,aAAa,KAAK,MAAM,IAAI;AAClC,YAAM,UAAiC,CAAC;AACxC,iBAAW,aAAa,YAAY;AAClC,cAAM,SAAS,OAAO,SAAS,SAAS;AACxC,YAAI,UAAU,MAAM;AAClB,kBAAQ,KAAK,MAAM;AAAA,QACrB;AAAA,MACF;AACA,aAAO;AAAA,IACT,QAAQ;AACN,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA,EAEQ,YAAY,WAAkC;AACpD,SAAK,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,KAAK,UAAU,UAAU,IAAI,CAAC,WAAW,OAAO,OAAO,CAAC,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EAEQ,sBACN,SACA,SACuB;AACvB,UAAM,SAAgC,CAAC;AAEvC,eAAW,UAAU,SAAS;AAC5B,UAAI,QAAQ,UAAU,CAAC,YAAY,QAAQ,QAAQ,OAAO,UAAU,EAAE,GAAG;AACvE;AAAA,MACF;AAEA,UAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,MAAM,OAAO,QAAQ,EAAE,GAAG;AAC/D;AAAA,MACF;AAEA,UAAI,QAAQ,OAAO,OAAO,QAAQ,QAAQ,KAAK;AAC7C;AAAA,MACF;AAEA,aAAO,KAAK,MAAM;AAAA,IACpB;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,sBACN,SACA,SACA;AACA,UAAM,kBAAkB,KAAK,sBAAsB,SAAS,OAAO;AACnE,WAAO,QAAQ,OAAO,CAAC,WAAW,CAAC,gBAAgB,SAAS,MAAM,CAAC;AAAA,EACrE;AACF;AAEA,MAAM,QAAQ,cAAc,IACxB,IAAI,kBAAkB,IACtB,IAAI,sBAAsB;AAEvB,MAAM,cAAc,IAAI,UAAU,KAAK;", "names": ["store"]}