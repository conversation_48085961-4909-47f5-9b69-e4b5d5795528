{"version": 3, "sources": ["../../src/core/HttpResponse.ts"], "sourcesContent": ["import { FetchResponse } from '@mswjs/interceptors'\nimport type { DefaultBodyType, JsonBodyType } from './handlers/RequestHandler'\nimport type { NoInfer } from './typeUtils'\nimport {\n  decorateResponse,\n  normalizeResponseInit,\n} from './utils/HttpResponse/decorators'\n\nexport interface HttpResponseInit extends ResponseInit {\n  type?: ResponseType\n}\n\nexport const bodyType: unique symbol = Symbol('bodyType')\nexport type DefaultUnsafeFetchResponse = Response & {\n  [bodyType]?: never\n}\n\nexport interface StrictRequest<BodyType extends JsonBodyType> extends Request {\n  json(): Promise<BodyType>\n}\n\n/**\n * Opaque `Response` type that supports strict body type.\n *\n * @deprecated Please use {@link HttpResponse} instead.\n */\nexport type StrictResponse<BodyType extends DefaultBodyType> =\n  HttpResponse<BodyType>\n\n/**\n * A drop-in replacement for the standard `Response` class\n * to allow additional features, like mocking the response `Set-<PERSON>ie` header.\n *\n * @example\n * new HttpResponse('Hello world', { status: 201 })\n * HttpResponse.json({ name: 'John' })\n * HttpResponse.formData(form)\n *\n * @see {@link https://mswjs.io/docs/api/http-response `HttpResponse` API reference}\n */\nexport class HttpResponse<\n  BodyType extends DefaultBodyType,\n> extends FetchResponse {\n  readonly [bodyType]: BodyType = null as any\n\n  constructor(body?: NoInfer<BodyType> | null, init?: HttpResponseInit) {\n    const responseInit = normalizeResponseInit(init)\n    super(body as BodyInit, responseInit)\n    decorateResponse(this, responseInit)\n  }\n\n  static error(): HttpResponse<any> {\n    return super.error() as HttpResponse<any>\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"text/plain\"` body.\n   * @example\n   * HttpResponse.text('hello world')\n   * HttpResponse.text('Error', { status: 500 })\n   */\n  static text<BodyType extends string>(\n    body?: NoInfer<BodyType> | null,\n    init?: HttpResponseInit,\n  ): HttpResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'text/plain')\n    }\n\n    // Automatically set the \"Content-Length\" response header\n    // for non-empty text responses. This enforces consistency and\n    // brings mocked responses closer to production.\n    if (!responseInit.headers.has('Content-Length')) {\n      responseInit.headers.set(\n        'Content-Length',\n        body ? new Blob([body]).size.toString() : '0',\n      )\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"application/json\"` body.\n   * @example\n   * HttpResponse.json({ firstName: 'John' })\n   * HttpResponse.json({ error: 'Not Authorized' }, { status: 401 })\n   */\n  static json<BodyType extends JsonBodyType>(\n    body?: NoInfer<BodyType> | null | undefined,\n    init?: HttpResponseInit,\n  ): HttpResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'application/json')\n    }\n\n    /**\n     * @note TypeScript is incorrect here.\n     * Stringifying undefined will return undefined.\n     */\n    const responseText = JSON.stringify(body) as string | undefined\n\n    if (!responseInit.headers.has('Content-Length')) {\n      responseInit.headers.set(\n        'Content-Length',\n        responseText ? new Blob([responseText]).size.toString() : '0',\n      )\n    }\n\n    return new HttpResponse(responseText as BodyType, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"application/xml\"` body.\n   * @example\n   * HttpResponse.xml(`<user name=\"John\" />`)\n   * HttpResponse.xml(`<article id=\"abc-123\" />`, { status: 201 })\n   */\n  static xml<BodyType extends string>(\n    body?: BodyType | null,\n    init?: HttpResponseInit,\n  ): HttpResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'text/xml')\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"text/html\"` body.\n   * @example\n   * HttpResponse.html(`<p class=\"author\">Jane Doe</p>`)\n   * HttpResponse.html(`<main id=\"abc-123\">Main text</main>`, { status: 201 })\n   */\n  static html<BodyType extends string>(\n    body?: BodyType | null,\n    init?: HttpResponseInit,\n  ): HttpResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'text/html')\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with an `ArrayBuffer` body.\n   * @example\n   * const buffer = new ArrayBuffer(3)\n   * const view = new Uint8Array(buffer)\n   * view.set([1, 2, 3])\n   *\n   * HttpResponse.arrayBuffer(buffer)\n   */\n  static arrayBuffer(\n    body?: ArrayBuffer | SharedArrayBuffer,\n    init?: HttpResponseInit,\n  ): HttpResponse<ArrayBuffer | SharedArrayBuffer> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'application/octet-stream')\n    }\n\n    if (body && !responseInit.headers.has('Content-Length')) {\n      responseInit.headers.set('Content-Length', body.byteLength.toString())\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `FormData` body.\n   * @example\n   * const data = new FormData()\n   * data.set('name', 'Alice')\n   *\n   * HttpResponse.formData(data)\n   */\n  static formData(\n    body?: FormData,\n    init?: HttpResponseInit,\n  ): HttpResponse<FormData> {\n    return new HttpResponse(body, normalizeResponseInit(init))\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAA8B;AAG9B,wBAGO;AAMA,MAAM,WAA0B,OAAO,UAAU;AA4BjD,MAAM,qBAEH,kCAAc;AAAA,EACtB,CAAU,QAAQ,IAAc;AAAA,EAEhC,YAAY,MAAiC,MAAyB;AACpE,UAAM,mBAAe,yCAAsB,IAAI;AAC/C,UAAM,MAAkB,YAAY;AACpC,4CAAiB,MAAM,YAAY;AAAA,EACrC;AAAA,EAEA,OAAO,QAA2B;AAChC,WAAO,MAAM,MAAM;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KACL,MACA,MACwB;AACxB,UAAM,mBAAe,yCAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,YAAY;AAAA,IACvD;AAKA,QAAI,CAAC,aAAa,QAAQ,IAAI,gBAAgB,GAAG;AAC/C,mBAAa,QAAQ;AAAA,QACnB;AAAA,QACA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI;AAAA,MAC5C;AAAA,IACF;AAEA,WAAO,IAAI,aAAa,MAAM,YAAY;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KACL,MACA,MACwB;AACxB,UAAM,mBAAe,yCAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,kBAAkB;AAAA,IAC7D;AAMA,UAAM,eAAe,KAAK,UAAU,IAAI;AAExC,QAAI,CAAC,aAAa,QAAQ,IAAI,gBAAgB,GAAG;AAC/C,mBAAa,QAAQ;AAAA,QACnB;AAAA,QACA,eAAe,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,SAAS,IAAI;AAAA,MAC5D;AAAA,IACF;AAEA,WAAO,IAAI,aAAa,cAA0B,YAAY;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,IACL,MACA,MACwB;AACxB,UAAM,mBAAe,yCAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,UAAU;AAAA,IACrD;AAEA,WAAO,IAAI,aAAa,MAAM,YAAY;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KACL,MACA,MACwB;AACxB,UAAM,mBAAe,yCAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,WAAW;AAAA,IACtD;AAEA,WAAO,IAAI,aAAa,MAAM,YAAY;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,YACL,MACA,MAC+C;AAC/C,UAAM,mBAAe,yCAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,0BAA0B;AAAA,IACrE;AAEA,QAAI,QAAQ,CAAC,aAAa,QAAQ,IAAI,gBAAgB,GAAG;AACvD,mBAAa,QAAQ,IAAI,kBAAkB,KAAK,WAAW,SAAS,CAAC;AAAA,IACvE;AAEA,WAAO,IAAI,aAAa,MAAM,YAAY;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,SACL,MACA,MACwB;AACxB,WAAO,IAAI,aAAa,UAAM,yCAAsB,IAAI,CAAC;AAAA,EAC3D;AACF;", "names": []}