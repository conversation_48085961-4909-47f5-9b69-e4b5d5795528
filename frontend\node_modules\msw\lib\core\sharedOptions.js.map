{"version": 3, "sources": ["../../src/core/sharedOptions.ts"], "sourcesContent": ["import type { Emitter } from 'strict-event-emitter'\nimport type { UnhandledRequestStrategy } from './utils/request/onUnhandledRequest'\n\nexport interface SharedOptions {\n  /**\n   * Specifies how to react to a request that has no corresponding\n   * request handler. Warns on unhandled requests by default.\n   *\n   * @example worker.start({ onUnhandledRequest: 'bypass' })\n   * @example worker.start({ onUnhandledRequest: 'warn' })\n   * @example server.listen({ onUnhandledRequest: 'error' })\n   */\n  onUnhandledRequest?: UnhandledRequestStrategy\n}\n\nexport type LifeCycleEventsMap = {\n  'request:start': [\n    args: {\n      request: Request\n      requestId: string\n    },\n  ]\n  'request:match': [\n    args: {\n      request: Request\n      requestId: string\n    },\n  ]\n  'request:unhandled': [\n    args: {\n      request: Request\n      requestId: string\n    },\n  ]\n  'request:end': [\n    args: {\n      request: Request\n      requestId: string\n    },\n  ]\n  'response:mocked': [\n    args: {\n      response: Response\n      request: Request\n      requestId: string\n    },\n  ]\n  'response:bypass': [\n    args: {\n      response: Response\n      request: Request\n      requestId: string\n    },\n  ]\n  unhandledException: [\n    args: {\n      error: Error\n      request: Request\n      requestId: string\n    },\n  ]\n}\n\nexport type LifeCycleEventEmitter<\n  EventsMap extends Record<string | symbol, any>,\n> = Pick<Emitter<EventsMap>, 'on' | 'removeListener' | 'removeAllListeners'>\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}