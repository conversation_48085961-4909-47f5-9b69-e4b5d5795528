{"version": 3, "sources": ["../../../../src/core/utils/url/getAbsoluteUrl.ts"], "sourcesContent": ["import { isAbsoluteUrl } from './isAbsoluteUrl'\n\n/**\n * Returns an absolute URL based on the given path.\n */\nexport function getAbsoluteUrl(path: string, baseUrl?: string): string {\n  // already absolute URL\n  if (isAbsoluteUrl(path)) {\n    return path\n  }\n\n  // Ignore path with pattern start with *\n  if (path.startsWith('*')) {\n    return path\n  }\n\n  // Resolve a relative request URL against a given custom \"baseUrl\"\n  // or the document baseURI (in the case of browser/browser-like environments).\n  const origin = baseUrl || (typeof location !== 'undefined' && location.href)\n\n  return origin\n    ? // Encode and decode the path to preserve escaped characters.\n      decodeURI(new URL(encodeURI(path), origin).href)\n    : path\n}\n"], "mappings": "AAAA,SAAS,qBAAqB;AAKvB,SAAS,eAAe,MAAc,SAA0B;AAErE,MAAI,cAAc,IAAI,GAAG;AACvB,WAAO;AAAA,EACT;AAGA,MAAI,KAAK,WAAW,GAAG,GAAG;AACxB,WAAO;AAAA,EACT;AAIA,QAAM,SAAS,WAAY,OAAO,aAAa,eAAe,SAAS;AAEvE,SAAO;AAAA;AAAA,IAEH,UAAU,IAAI,IAAI,UAAU,IAAI,GAAG,MAAM,EAAE,IAAI;AAAA,MAC/C;AACN;", "names": []}