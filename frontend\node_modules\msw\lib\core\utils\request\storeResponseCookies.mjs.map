{"version": 3, "sources": ["../../../../src/core/utils/request/storeResponseCookies.ts"], "sourcesContent": ["import { cookieStore } from '../cookieStore'\nimport { kSetCookie } from '../HttpResponse/decorators'\n\nexport function storeResponseCookies(\n  request: Request,\n  response: Response,\n): void {\n  // Grab the raw \"Set-Cookie\" response header provided\n  // in the HeadersInit for this mocked response.\n  const responseCookies = Reflect.get(response, kSetCookie) as\n    | string\n    | undefined\n\n  if (responseCookies) {\n    cookieStore.setCookie(responseCookies, request.url)\n  }\n}\n"], "mappings": "AAAA,SAAS,mBAAmB;AAC5B,SAAS,kBAAkB;AAEpB,SAAS,qBACd,SACA,UACM;AAGN,QAAM,kBAAkB,QAAQ,IAAI,UAAU,UAAU;AAIxD,MAAI,iBAAiB;AACnB,gBAAY,UAAU,iBAAiB,QAAQ,GAAG;AAAA,EACpD;AACF;", "names": []}