{"version": 3, "sources": ["../../../../src/core/utils/matching/normalizePath.ts"], "sourcesContent": ["import type { Path } from './matchRequestUrl'\nimport { cleanUrl } from '../url/cleanUrl'\nimport { getAbsoluteUrl } from '../url/getAbsoluteUrl'\n\n/**\n * Normalizes a given request handler path:\n * - Preserves RegExp.\n * - Removes query parameters and hashes.\n * - Rebases relative URLs against the \"baseUrl\" or the current location.\n * - Preserves relative URLs in Node.js, unless specified otherwise.\n * - Preserves optional path parameters.\n */\nexport function normalizePath(path: Path, baseUrl?: string): Path {\n  // RegExp paths do not need normalization.\n  if (path instanceof RegExp) {\n    return path\n  }\n\n  const maybeAbsoluteUrl = getAbsoluteUrl(path, baseUrl)\n\n  return cleanUrl(maybeAbsoluteUrl)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAyB;AACzB,4BAA+B;AAUxB,SAAS,cAAc,MAAY,SAAwB;AAEhE,MAAI,gBAAgB,QAAQ;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,uBAAmB,sCAAe,MAAM,OAAO;AAErD,aAAO,0BAAS,gBAAgB;AAClC;", "names": []}