{"version": 3, "sources": ["../../../src/core/utils/toResponseInit.ts"], "sourcesContent": ["export function toResponseInit(response: Response): ResponseInit {\n  return {\n    status: response.status,\n    statusText: response.statusText,\n    headers: Object.fromEntries(response.headers.entries()),\n  }\n}\n"], "mappings": "AAAO,SAAS,eAAe,UAAkC;AAC/D,SAAO;AAAA,IACL,QAAQ,SAAS;AAAA,IACjB,YAAY,SAAS;AAAA,IACrB,SAAS,OAAO,YAAY,SAAS,QAAQ,QAAQ,CAAC;AAAA,EACxD;AACF;", "names": []}