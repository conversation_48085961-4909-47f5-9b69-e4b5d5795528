{"version": 3, "sources": ["../../../../src/core/utils/internal/isObject.ts"], "sourcesContent": ["/**\n * Determines if the given value is an object.\n */\nexport function isObject(value: any): boolean {\n  return value != null && typeof value === 'object' && !Array.isArray(value)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,SAAS,OAAqB;AAC5C,SAAO,SAAS,QAAQ,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK;AAC3E;", "names": []}