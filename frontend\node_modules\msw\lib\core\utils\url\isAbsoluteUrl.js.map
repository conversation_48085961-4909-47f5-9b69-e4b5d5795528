{"version": 3, "sources": ["../../../../src/core/utils/url/isAbsoluteUrl.ts"], "sourcesContent": ["/**\n * Determines if the given URL string is an absolute URL.\n */\nexport function isAbsoluteUrl(url: string): boolean {\n  return /^([a-z][a-z\\d\\+\\-\\.]*:)?\\/\\//i.test(url)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,cAAc,KAAsB;AAClD,SAAO,gCAAgC,KAAK,GAAG;AACjD;", "names": []}