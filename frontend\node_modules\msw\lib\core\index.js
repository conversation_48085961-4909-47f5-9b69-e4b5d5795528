"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var index_exports = {};
__export(index_exports, {
  GraphQLHandler: () => import_GraphQLHandler.GraphQLHandler,
  HttpHandler: () => import_HttpHandler.HttpHandler,
  HttpMethods: () => import_HttpHandler.HttpMethods,
  RequestHandler: () => import_RequestHandler.RequestHandler,
  SetupApi: () => import_SetupApi.SetupApi,
  WebSocketHandler: () => import_WebSocketHandler.WebSocketHandler,
  bypass: () => import_bypass.bypass,
  cleanUrl: () => import_cleanUrl.cleanUrl,
  getResponse: () => import_getResponse.getResponse,
  graphql: () => import_graphql.graphql,
  http: () => import_http.http,
  isCommonAssetRequest: () => import_isCommonAssetRequest.isCommonAssetRequest,
  matchRequestUrl: () => import_matchRequestUrl.matchRequestUrl,
  passthrough: () => import_passthrough.passthrough,
  ws: () => import_ws.ws
});
module.exports = __toCommonJS(index_exports);
var import_checkGlobals = require("./utils/internal/checkGlobals");
var import_SetupApi = require("./SetupApi");
var import_RequestHandler = require("./handlers/RequestHandler");
var import_http = require("./http");
var import_HttpHandler = require("./handlers/HttpHandler");
var import_graphql = require("./graphql");
var import_GraphQLHandler = require("./handlers/GraphQLHandler");
var import_ws = require("./ws");
var import_WebSocketHandler = require("./handlers/WebSocketHandler");
var import_matchRequestUrl = require("./utils/matching/matchRequestUrl");
__reExport(index_exports, require("./utils/handleRequest"), module.exports);
var import_getResponse = require("./getResponse");
var import_cleanUrl = require("./utils/url/cleanUrl");
__reExport(index_exports, require("./HttpResponse"), module.exports);
__reExport(index_exports, require("./delay"), module.exports);
var import_bypass = require("./bypass");
var import_passthrough = require("./passthrough");
var import_isCommonAssetRequest = require("./isCommonAssetRequest");
(0, import_checkGlobals.checkGlobals)();
//# sourceMappingURL=index.js.map