{"version": 3, "sources": ["../../../../src/core/utils/internal/pipeEvents.ts"], "sourcesContent": ["import { Emitter, EventMap } from 'strict-event-emitter'\n\n/**\n * Pipes all emitted events from one emitter to another.\n */\nexport function pipeEvents<Events extends EventMap>(\n  source: Emitter<Events>,\n  destination: Emitter<Events>,\n): void {\n  const rawEmit: typeof source.emit & { _isPiped?: boolean } = source.emit\n\n  if (rawEmit._isPiped) {\n    return\n  }\n\n  const sourceEmit: typeof source.emit & { _isPiped?: boolean } =\n    function sourceEmit(this: typeof source, event, ...data) {\n      destination.emit(event, ...data)\n      return rawEmit.call(this, event, ...data)\n    }\n\n  sourceEmit._isPiped = true\n\n  source.emit = sourceEmit\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKO,SAAS,WACd,QACA,aACM;AACN,QAAM,UAAuD,OAAO;AAEpE,MAAI,QAAQ,UAAU;AACpB;AAAA,EACF;AAEA,QAAM,aACJ,SAASA,YAAgC,UAAU,MAAM;AACvD,gBAAY,KAAK,OAAO,GAAG,IAAI;AAC/B,WAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,IAAI;AAAA,EAC1C;AAEF,aAAW,WAAW;AAEtB,SAAO,OAAO;AAChB;", "names": ["sourceEmit"]}