{"version": 3, "sources": ["../../src/core/getResponse.ts"], "sourcesContent": ["import { createRequestId } from '@mswjs/interceptors'\nimport type { RequestHandler } from './handlers/RequestHandler'\nimport { executeHandlers } from './utils/executeHandlers'\n\n/**\n * Finds a response for the given request instance\n * in the array of request handlers.\n * @param handlers The array of request handlers.\n * @param request The `Request` instance.\n * @returns {Response} A mocked response, if any.\n */\nexport const getResponse = async (\n  handlers: Array<RequestHandler>,\n  request: Request,\n): Promise<Response | undefined> => {\n  const result = await executeHandlers({\n    request,\n    requestId: createRequestId(),\n    handlers,\n  })\n\n  return result?.response\n}\n"], "mappings": "AAAA,SAAS,uBAAuB;AAEhC,SAAS,uBAAuB;AASzB,MAAM,cAAc,OACzB,UACA,YACkC;AAClC,QAAM,SAAS,MAAM,gBAAgB;AAAA,IACnC;AAAA,IACA,WAAW,gBAAgB;AAAA,IAC3B;AAAA,EACF,CAAC;AAED,SAAO,QAAQ;AACjB;", "names": []}